"""
Agents for the Travel Assistant.
This module defines all the agents used in the application.
"""
from langchain_openai import ChatOpenA<PERSON>
from langchain_xai import ChatXA<PERSON>
from langgraph.prebuilt import create_react_agent
from utils.config import OPENAI_API_KEY, OPENAI_MODEL, DEFAULT_TEMPERATURE
from .prompts import (
    TOUR_AGENT_PROMPT,
    TICKET_AGENT_PROMPT,
    FLIGHT_AGENT_PROMPT,
    HOTEL_AGENT_PROMPT,
    VISA_AGENT_PROMPT,
    ESIM_AGENT_PROMPT
)
# Import new tools
from tools.search_flight import search_flight, booking_flight
from tools.search_tour import search_tour, get_more_tour_info, booking_tour
from tools.search_hotel import search_hotel, get_more_hotel_info, booking_hotel
from tools.search_ticket import search_ticket
from tools.search_visa import search_visa
from tools.search_esim import search_esim
from tools.search_policy_airplane import search_policy_airplane
from tools.search_flight_info import search_flight_info

# # Define the model to use for all agents
model = ChatOpenAI(
    model=OPENAI_MODEL,
    temperature=DEFAULT_TEMPERATURE,
    openai_api_key=OPENAI_API_KEY
)

# Tour Agent
tour_agent = create_react_agent(
    model=model,
    tools=[search_tour, get_more_tour_info, booking_tour],
    name="tour_agent",
    prompt=TOUR_AGENT_PROMPT
)

# Ticket Agent
ticket_agent = create_react_agent(
    model=model,
    tools=[search_ticket],
    name="ticket_agent",
    prompt=TICKET_AGENT_PROMPT
)

# Flight Agent
flight_agent = create_react_agent(
    model=model,
    tools=[ booking_flight, search_policy_airplane, search_flight_info, search_flight],
    name="flight_agent",
    prompt=FLIGHT_AGENT_PROMPT
)

# Hotel Agent
hotel_agent = create_react_agent(
    model=model,
    tools=[search_hotel, get_more_hotel_info, booking_hotel],
    name="hotel_agent",
    prompt=HOTEL_AGENT_PROMPT
)

# Visa Agent
visa_agent = create_react_agent(
    model=model,
    tools=[search_visa],
    name="visa_agent",
    prompt=VISA_AGENT_PROMPT
)

# eSIM Agent
esim_agent = create_react_agent(
    model=model,
    tools=[search_esim],
    name="esim_agent",
    prompt=ESIM_AGENT_PROMPT
)


