"""
Supervisor implementation using langgraph_supervisor.
This module defines the supervisor that routes user queries to specialized agents.
"""
import logging
from langchain_openai import ChatOpenAI
from langgraph_supervisor import create_supervisor
from utils.config import OPENAI_API_KEY, OPENAI_MODEL, SUPERVISOR_TEMPERATURE
from agents.prompts import SUPERVISOR_PROMPT
from agents.agents import (
    tour_agent,
    flight_agent,
    ticket_agent,
    hotel_agent,
    visa_agent,
    esim_agent
)
from tools.search_web import search_web
from tools.search_policy_airplane import search_policy_airplane
from tools.odoo_manipulation import create_lead, update_lead, reconfirm

# Set up logging
logger = logging.getLogger("supervisor")

# Define the model to use for the supervisor
model = ChatOpenAI(
    model=OPENAI_MODEL,
    temperature=SUPERVISOR_TEMPERATURE,
    openai_api_key=OPENAI_API_KEY
)

# Create the supervisor graph
supervisor = create_supervisor(
    agents=[
        tour_agent,
        flight_agent,
        ticket_agent,
        hotel_agent,
        visa_agent,
        esim_agent
    ],
    tools=[search_web, create_lead, update_lead, reconfirm, search_policy_airplane],
    model=model,
    prompt=SUPERVISOR_PROMPT,
    output_mode="last_message",  # Changed to preserve only the last message from agents
    add_handoff_back_messages=True,  # Keep False to avoid extra messages
    add_handoff_messages=True,  # Changed to False to minimize message modification
)
