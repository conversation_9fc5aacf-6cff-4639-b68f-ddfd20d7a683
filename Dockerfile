FROM python:3.11-slim

WORKDIR /app

# <PERSON>o chép các file cần thiết
COPY . .

# Cài đặt các dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Cài đặt langgraph-cli
RUN pip install --no-cache-dir --upgrade "langgraph-cli[inmem]"

# No need to install as a package since we're copying all files directly
# The application will run from the copied files

# Mở port 80
EXPOSE 80

# Chạy LangGraph server
CMD ["langgraph", "dev", "--host", "0.0.0.0", "--port", "80", "--allow-blocking"]