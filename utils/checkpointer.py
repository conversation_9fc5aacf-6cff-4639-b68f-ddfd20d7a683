import uuid
import os
import logging
import traceback
from typing import Union, TypeVar, Optional
from dotenv import load_dotenv
from datetime import datetime

# Import checkpointers
from langgraph.checkpoint.memory import InMemorySaver
# Import the MongoDB checkpointer
from langgraph.checkpoint.mongodb import MongoDBSaver
# Import store for cross-thread persistence
from langgraph.store.memory import InMemoryStore

# Define a type variable for the checkpointer
CheckpointerType = TypeVar('CheckpointerType', bound=Union[MongoDBSaver, InMemorySaver])

# Import MongoDB connection utility
from utils.mongodb_connection import get_mongodb_client, get_collection_name

# Set up logging
logger = logging.getLogger("checkpointer")

# Load environment variables
load_dotenv()

def get_checkpointer() -> Union[MongoDBSaver, InMemorySaver]:
    """Get a checkpointer instance.

    Returns MongoDB checkpointer if MongoDB is configured,
    otherwise falls back to InMemorySaver.

    Returns:
        A checkpointer instance for persistence
    """
    # For LangGraph dev, check if blocking is allowed
    # based on environment variable
    allow_blocking = os.getenv("BG_JOB_ISOLATED_LOOPS", "false").lower() == "true"
    is_langgraph_dev = os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true"

    # If running in LangGraph dev mode and blocking is not allowed, use in-memory storage
    if is_langgraph_dev and not allow_blocking:
        logger.info("Running in LangGraph server mode with blocking not allowed, using in-memory storage")
        return InMemorySaver()

    # Check if MongoDB should be used (from environment variable)
    use_mongodb = os.getenv("USE_MONGODB", "true").lower() == "true"

    # If MongoDB is disabled, use in-memory storage
    if not use_mongodb:
        logger.info("Using in-memory storage (MongoDB is disabled)")
        return InMemorySaver()

    # Use synchronous MongoDB (with blocking calls)
    try:
        # Get MongoDB client
        client = get_mongodb_client()

        # Get collection name from environment variable
        collection_name = get_collection_name()

        # Get writes collection name from environment variable
        writes_collection_name = os.getenv("ATLAS_COLLECTION_WRITES", "checkpoint_writes")

        # Get database name from environment variable
        db_name = os.getenv("ATLAS_DB_NAME", "langgraph_checkpointing_db")

        logger.info(f"Using MongoDB for conversation storage with database: {db_name}, collection: {collection_name}")
        # Create and return MongoDB checkpointer with standard parameters
        return MongoDBSaver(
            client,
            db_name=db_name,
            checkpoint_collection_name=collection_name,
            writes_collection_name=writes_collection_name
        )
    except Exception as e:
        logger.error(f"Could not initialize MongoDB checkpointer: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.error(f"Exception details: {str(e)}")

        # Log the traceback
        tb = traceback.format_exc()
        logger.error(f"Traceback: {tb}")

        logger.warning("Using in-memory storage as fallback")
        return InMemorySaver()

def get_memory_store() -> InMemoryStore:
    """Get a memory store for cross-thread persistence.

    Returns:
        An InMemoryStore instance for cross-thread persistence
    """
    # Create an in-memory store for cross-thread persistence
    try:
        # Check if we should use embeddings for semantic search
        use_embeddings = os.getenv("USE_EMBEDDINGS", "false").lower() == "true"

        if use_embeddings:
            try:
                # Import embedding model
                from langchain.embeddings import init_embeddings

                # Create store with semantic search capabilities
                logger.info("Creating memory store with semantic search capabilities")
                return InMemoryStore(
                    index={
                        "embed": init_embeddings("openai:text-embedding-3-small"),
                        "dims": 1536,
                        "fields": ["$"]  # Index all fields
                    }
                )
            except Exception as e:
                logger.error(f"Error initializing embeddings: {e}")
                logger.warning("Falling back to basic memory store without semantic search")
                return InMemoryStore()
        else:
            # Create basic in-memory store
            logger.info("Creating basic memory store without semantic search")
            return InMemoryStore()
    except Exception as e:
        logger.error(f"Error creating memory store: {e}")
        # Fall back to basic store
        return InMemoryStore()

def get_thread_id() -> str:
    """Generate a unique thread ID.

    Returns:
        A unique thread ID as a string
    """
    return str(uuid.uuid4())

def get_timestamp() -> str:
    """Get the current timestamp in ISO format.

    Returns:
        Current timestamp as a string in ISO format
    """
    return datetime.now().isoformat()

def create_config(thread_id: Optional[str] = None, user_id: Optional[str] = None) -> dict:
    """Create a configuration dictionary for graph invocation.

    Args:
        thread_id: Optional thread ID. If not provided, a new one will be generated.
        user_id: Optional user ID for cross-thread persistence.

    Returns:
        A configuration dictionary for graph invocation
    """
    if thread_id is None:
        thread_id = get_thread_id()

    config = {"configurable": {"thread_id": thread_id}}

    # Add user_id if provided
    if user_id is not None:
        config["configurable"]["user_id"] = user_id

    return config