from typing import Dict, List, Literal, Optional, TypedDict
from typing_extensions import Annotated
from langchain_core.messages import SystemMessage, BaseMessage
import operator
from datetime import datetime

# Define the possible agent types for routing
AgentType = Literal[
    "tour_agent",
    "flight_agent",
    "hotel_agent",
    "visa_agent",
    "esim_agent",
    "ticket_agent",
    "knowledge_agent",
    "lead_agent",
    "__end__"
]

# Define the state schema for our application
class AgentState(TypedDict):
    """State for the travel agent application."""
    # Messages exchanged in the conversation - using operator.add as reducer to append new messages
    messages: Annotated[List[BaseMessage], operator.add]
    # Current agent handling the request
    current_agent: Optional[AgentType]
    # Next agent to route to
    next_agent: Optional[AgentType]
    # User query information
    user_query: Optional[str]
    # Additional context or information
    context: Dict
    # Final response to be sent to the user
    final_response: Optional[str]
    # Memory for data sharing between agents
    agent_memory: Dict
    # Timestamp for when the request was made
    timestamp: Optional[str]

# Initialize default state
def get_initial_state() -> AgentState:
    """Initialize the default state for the application."""
    return {
        "messages": [
            SystemMessage(content="Bạn là trợ lý du lịch giúp người dùng với các dịch vụ liên quan đến du lịch.")
        ],
        "current_agent": None,
        "next_agent": None,
        "user_query": None,
        "context": {},
        "final_response": None,
        "agent_memory": {},
        "timestamp": datetime.now().isoformat()
    }