import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# OpenAI API configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
OPENAI_MODEL = os.environ.get("OPENAI_MODEL", "gpt-4o-mini")
XAI_API_KEY = os.environ.get("XAi_API_KEY", "")
XAI_MODEL = os.environ.get("XAi_MODEL", "grok-2-1212")

# LLM configuration
DEFAULT_TEMPERATURE = 0.7
SUPERVISOR_TEMPERATURE = 0.2  # Lower temperature for more deterministic routing

# Odoo configuration
ODOO_BASE_URL = os.getenv("ODOO_BASE_URL")
ODOO_DATABASE_NAME = os.getenv("ODOO_DATABASE_NAME")
ODOO_USER_NAME = os.getenv("ODOO_USER_NAME")
ODOO_PASSWORD = os.getenv("ODOO_PASSWORD")

# AI Rovi Web configuration
RETRIEVAL_API_URL = os.getenv("RETRIEVAL_API_URL")
AI_ROVI_TRAVEL_TOKEN = os.getenv("AI_ROVI_TRAVEL_TOKEN")
KNOWLEDGE_BASE_API = os.getenv("KNOWLEDGE_BASE_API_URL")

# Flight search configuration
FLIGHT_API_TOKEN = os.getenv("FLIGHT_API_TOKEN")
FLIGHT_API_URL = os.getenv("FLIGHT_API_URL")
FLIGHT_API_DETAIL_URL = os.getenv("FLIGHT_API_DETAIL_URL")

# Hotel search configuration
HOTEL_SEARCH_URL = os.getenv("HOTEL_SEARCH_URL")
HOTEL_CORE_URL = os.getenv("HOTEL_CORE_URL")
DEV_SITE_KEY = os.getenv("DEV_SITE_KEY")

# Tour search configuration
TOUR_API_URL = os.getenv("TOUR_SEARCH_URL")
TOUR_BOOKING_URL = os.getenv("TOUR_BOOKING_URL")
SITE_KEY = os.getenv("SITE_KEY")

# Email configuration
EMAIL_SENDER = os.getenv("EMAIL_SENDER")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD")

# Tavily API configuration
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")