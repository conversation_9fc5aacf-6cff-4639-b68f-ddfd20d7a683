"""
MongoDB connection utility for LangGraph persistence.
Supports both synchronous and asynchronous connections.
"""
import os
import asyncio
from typing import Optional
import logging
from dotenv import load_dotenv

# Set up logging
logger = logging.getLogger("mongodb_connection")

# Load environment variables
load_dotenv()

# Default MongoDB configuration
DEFAULT_MONGODB_URL = "mongodb://localhost:27017/?directConnection=true"
DEFAULT_DB_NAME = "langgraph_checkpointing_db"
DEFAULT_COLLECTION_NAME = "checkpoints"

# Global variables for client instances (connection pooling)
_mongo_client_sync = None
_mongo_client_async = None


def get_mongodb_client_sync():
    """
    Get a synchronous MongoDB client instance with connection pooling.

    Returns:
        MongoClient: A synchronous MongoDB client instance
    """
    global _mongo_client_sync

    # Import here to avoid import errors if motor is not installed
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, ConfigurationError

    # If client already exists, return it
    if _mongo_client_sync is not None:
        return _mongo_client_sync

    # Get MongoDB connection info from environment variables or use default
    mongodb_url = os.getenv("ATLAS_URL", DEFAULT_MONGODB_URL)

    if not mongodb_url:
        logger.warning("ATLAS_URL not configured in .env file. Using default URL.")

    try:
        # Log the MongoDB URL (without sensitive info)
        safe_url = mongodb_url.split("@")[-1] if "@" in mongodb_url else mongodb_url
        logger.info(f"Attempting to connect to MongoDB at: {safe_url} (sync)")

        # Create a new client instance with appropriate options
        _mongo_client_sync = MongoClient(
            mongodb_url,
            connectTimeoutMS=30000,  # Connection timeout (30 seconds)
            socketTimeoutMS=45000,   # Socket timeout (45 seconds)
            serverSelectionTimeoutMS=30000,  # Server selection timeout (30 seconds)
            maxPoolSize=100,         # Maximum connection pool size
        )

        # Test connection
        logger.info("Testing MongoDB connection with ping command...")
        _mongo_client_sync.admin.command('ping')

        logger.info("Successfully connected to MongoDB (sync)")
        return _mongo_client_sync

    except ConnectionFailure as e:
        logger.error(f"Could not connect to MongoDB: {e}")
        raise ConnectionError(f"Could not connect to MongoDB: {e}")

    except ConfigurationError as e:
        logger.error(f"MongoDB configuration error: {e}")
        raise ConfigurationError(f"MongoDB configuration error: {e}")

    except Exception as e:
        logger.error(f"Unexpected error connecting to MongoDB: {e}")
        raise Exception(f"Unexpected error connecting to MongoDB: {e}")


async def get_mongodb_client_async():
    """
    Get an asynchronous MongoDB client instance with connection pooling.

    Returns:
        AsyncIOMotorClient: An asynchronous MongoDB client instance
    """
    global _mongo_client_async

    # Import here to avoid import errors if motor is not installed
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
    except ImportError:
        logger.error("Motor package not installed. Please install with 'pip install motor'")
        raise ImportError("Motor package not installed. Please install with 'pip install motor'")

    # If client already exists, return it
    if _mongo_client_async is not None:
        return _mongo_client_async

    # Get MongoDB connection info from environment variables or use default
    mongodb_url = os.getenv("ATLAS_URL", DEFAULT_MONGODB_URL)

    if not mongodb_url:
        logger.warning("ATLAS_URL not configured in .env file. Using default URL.")

    try:
        # Log the MongoDB URL (without sensitive info)
        safe_url = mongodb_url.split("@")[-1] if "@" in mongodb_url else mongodb_url
        logger.info(f"Attempting to connect to MongoDB at: {safe_url} (async)")

        # Create a new client instance with appropriate options
        _mongo_client_async = AsyncIOMotorClient(
            mongodb_url,
            connectTimeoutMS=30000,  # Connection timeout (30 seconds)
            socketTimeoutMS=45000,   # Socket timeout (45 seconds)
            serverSelectionTimeoutMS=30000,  # Server selection timeout (30 seconds)
            maxPoolSize=100,         # Maximum connection pool size
            io_loop=asyncio.get_event_loop(),  # Explicitly set the event loop
        )

        # Test connection (async)
        logger.info("Testing async MongoDB connection...")
        await _mongo_client_async.admin.command('ping')

        logger.info("Successfully connected to MongoDB (async)")
        return _mongo_client_async

    except Exception as e:
        logger.error(f"Unexpected error connecting to MongoDB (async): {e}")
        raise Exception(f"Unexpected error connecting to MongoDB (async): {e}")


def get_mongodb_client():
    """
    Get a MongoDB client instance with connection pooling.
    This is a compatibility function that returns the synchronous client.

    Returns:
        MongoClient: A synchronous MongoDB client instance
    """
    return get_mongodb_client_sync()


def get_database(database_name: Optional[str] = None):
    """
    Get a MongoDB database instance.

    Args:
        database_name (str, optional): Name of the database.
                                      If None, use from environment variables.

    Returns:
        Database: A MongoDB database instance
    """
    client = get_mongodb_client()

    if database_name is None:
        # Get database name from environment variables
        database_name = os.getenv("ATLAS_DB_NAME", DEFAULT_DB_NAME)

    return client[database_name]


def get_collection_name() -> str:
    """
    Get collection name from environment variables.

    Returns:
        str: Collection name
    """
    return os.getenv("ATLAS_COLLECTION_DATA", DEFAULT_COLLECTION_NAME)


def get_writes_collection_name() -> str:
    """
    Get writes collection name from environment variables.

    Returns:
        str: Writes collection name
    """
    return os.getenv("ATLAS_COLLECTION_WRITES", "checkpoint_writes")


def close_mongodb_connection():
    """
    Close MongoDB connections (both sync and async).
    """
    global _mongo_client_sync, _mongo_client_async

    if _mongo_client_sync is not None:
        _mongo_client_sync.close()
        _mongo_client_sync = None
        logger.info("Synchronous MongoDB connection closed")

    if _mongo_client_async is not None:
        _mongo_client_async.close()
        _mongo_client_async = None
        logger.info("Asynchronous MongoDB connection closed")
