{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LangGraph Visualization\n", "\n", "This notebook demonstrates how to visualize your LangGraph graph as a PNG image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with '.venv (Python 3.12.3)' requires the ipykernel package.\n", "\u001b[1;31m<PERSON><PERSON>all 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: '/home/<USER>/AI.ROVI/langgraph_docs/.venv/bin/python -m pip install ipykernel -U --force-reinstall'"]}], "source": ["# Import necessary libraries\n", "from IPython.display import Image, display\n", "from app import create_travel_agent_graph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create and visualize the graph\n", "\n", "First, we'll create the graph using the function from app.py:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the graph\n", "graph = create_travel_agent_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's visualize the graph using the Mermaid PNG renderer:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display the graph visualization directly in the notebook\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save the visualization to a file\n", "\n", "You can also save the visualization to a file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the visualization to a file\n", "png_data = graph.get_graph().draw_mermaid_png()\n", "with open(\"graph.png\", \"wb\") as f:\n", "    f.write(png_data)\n", "    \n", "print(\"Graph visualization saved to graph.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alternative visualization methods\n", "\n", "LangGraph provides several other visualization methods:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get the Mermaid code\n", "mermaid_code = graph.get_graph().draw_mermaid()\n", "print(mermaid_code)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get ASCII representation\n", "ascii_graph = graph.get_graph().draw_ascii()\n", "print(ascii_graph)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}