#!/bin/bash

# Script để chạy LangGraph Server

echo "Khởi động LangGraph Server cho Travel Assistant..."

# Kiểm tra xem langgraph-cli đã được cài đặt chưa
if ! command -v langgraph &> /dev/null
then
    echo "langgraph-cli chưa được cài đặt. Đang cài đặt..."
    pip install --upgrade "langgraph-cli[inmem]"
fi

# Kiểm tra file .env
if [ ! -f .env ]; then
    echo "Lỗi: Không tìm thấy file .env!"
    exit 1
fi

# Kiểm tra LangSmith API key
if ! grep -q "LANGSMITH_API_KEY" .env; then
    echo "Lỗi: LANGSMITH_API_KEY không tìm thấy trong file .env!"
    exit 1
fi

# Khởi động LangGraph Server
echo "Khởi động LangGraph Server..."

# Kiểm tra xem MongoDB có được bật không
if grep -q "USE_MONGODB=true" .env; then
    echo "MongoDB được bật. Khởi động server với flag --allow-blocking..."
    langgraph dev --allow-blocking
else
    echo "MongoDB bị tắt. Khởi động server ở chế độ in-memory..."
    langgraph dev
fi

echo "LangGraph Server đã dừng."
