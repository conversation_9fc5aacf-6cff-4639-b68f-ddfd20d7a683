"""
Main LangGraph application for the Travel Assistant.
This module defines the graph structure for the LangGraph server.
"""
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv
import logging
import os

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("travel_assistant")

# Import state definition
from utils.state import get_initial_state

# Import utilities for persistence and configuration
from utils.checkpointer import (
    get_checkpointer,
    get_memory_store,
    get_timestamp,
    create_config
)

# Import the supervisor implementation
from agents.supervisor import supervisor

def create_travel_agent_graph():
    """Create the travel agent graph using langgraph_supervisor.

    This function creates and compiles the travel agent graph with
    persistence features (checkpointer and memory store).

    Returns:
        A compiled LangGraph graph ready for execution
    """
    # Get the checkpointer for thread-level persistence
    checkpointer = get_checkpointer()
    logger.info(f"Using checkpointer: {type(checkpointer).__name__}")

    # Get the memory store for cross-thread persistence
    memory_store = get_memory_store()
    logger.info(f"Using memory store: {type(memory_store).__name__}")

    # Compile with the checkpointer and memory store
    return supervisor.compile(checkpointer=checkpointer, store=memory_store)

# Create the graph
graph = create_travel_agent_graph()

# For testing purposes
if __name__ == "__main__":
    # Create initial state
    state = get_initial_state()

    # Add timestamp to state
    if "timestamp" in state:
        state["timestamp"] = get_timestamp()

    # Add a test message
    test_message = os.getenv("TEST_MESSAGE", "I want to book a tour to Japan")
    state["messages"].append(HumanMessage(content=test_message))

    # Create a user ID for cross-thread persistence (in a real app, this would be the actual user ID)
    user_id = "test_user_1"

    # Create a config with thread_id and user_id
    config = create_config(user_id=user_id)
    logger.info(f"Using thread_id: {config['configurable']['thread_id']}")
    logger.info(f"Using user_id: {config['configurable']['user_id']}")

    # Invoke the graph with streaming for better visibility during testing
    logger.info("Invoking graph with streaming...")
    for chunk in graph.stream(state, config=config, stream_mode="values"):
        # Process each chunk as it arrives
        if "messages" in chunk and chunk["messages"]:
            latest_message = chunk["messages"][-1]
            if hasattr(latest_message, "content"):
                print(f"[{latest_message.type}]: {latest_message.content}")

    # For comparison, also invoke without streaming to get the final result
    logger.info("Invoking graph without streaming...")
    result = graph.invoke(state, config=config)

    # Extract the final response from the last AI message
    final_response = "No response generated"
    if "messages" in result:
        for msg in reversed(result["messages"]):
            if hasattr(msg, "type") and msg.type == "ai":
                final_response = msg.content
                break

    # Print the result
    print("\nFinal response:", final_response)
