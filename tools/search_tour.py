from pydantic import BaseModel, <PERSON>
from typing import Optional
import requests
import json
import logging
import os
from datetime import datetime, timedelta
from langchain_core.tools import tool, ToolException
from langchain_core.runnables import RunnableConfig
from utils.config import TOUR_API_URL, TOUR_BOOKING_URL, SITE_KEY


# Define input schemas for better type checking and documentation
class TourSearchInput(BaseModel):
    """Input schema for tour search"""
    chat_id: str = Field(description="The chat ID associated with the user session")
    query: str = Field(description="The search query containing tour preferences and location")

class TourInfoInput(BaseModel):
    """Input schema for detailed tour information"""
    tour_code: str = Field(description="The unique identifier of the tour")
    from_date: Optional[str] = Field(default="", description="Start date of the tour range (YYYY-MM-DD)")
    to_date: Optional[str] = Field(default="", description="End date of the tour range (YYYY-MM-DD)")

@tool("search_tour", args_schema=TourSearchInput)
def search_tour(
    chat_id: str,
    query: str,
    config: RunnableConfig = None,
) -> str:
    """
    Search for tours based on a user query. Use only for general TOUR information queries (excluding price).

    Args:
        chat_id: The chat ID associated with the user session.
        query: The search query containing tour preferences and location.
        config: Runtime configuration (automatically injected).

    Returns:
        A string containing the search results in JSON format or an error message.
    """
    base_url = TOUR_API_URL
    site_key = SITE_KEY

    headers = {
        "accept": "application/json",
        "siteKey": site_key,
        "Content-Type": "application/json",
    }
    payload = {"chat_id": chat_id, "query": query}

    logging.info(f"Tour search query: {query} for chat_id: {chat_id}")

    try:
        logging.info(f"Making API request to: {base_url} with timeout: 5s")
        response = requests.post(base_url, json=payload, headers=headers, timeout=5)

        # Log the response status and headers
        logging.info(f"Response status: {response.status_code}")
        logging.info(f"Response headers: {response.headers}")

        response.raise_for_status()  # Raise error for bad status

        # Try to parse the JSON response
        try:
            data = response.json()
            logging.info(f"Response data structure: {list(data.keys())}")

            if "result" in data and "message" in data["result"] and data["result"]["message"]:
                logging.info(f"Found {len(data['result']['message'])} tours")
                return json.dumps(data["result"]["message"])
            else:
                logging.warning(f"No tours found in response: {data}")
                return "No tours available."

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from tour API: {e}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error searching for tours: {str(e)}")

@tool("get_more_tour_info", args_schema=TourInfoInput)
def get_more_tour_info(
    tour_code: str,
    from_date: str = "",
    to_date: str = "",
    config: RunnableConfig = None,
) -> str:
    """
    Retrieve detailed information for a specific tour within a given date range.
    Use this for fetching tour data by code.

    Args:
        tour_code: The unique identifier of the tour to retrieve information for.
        from_date: Start date of the tour range (YYYY-MM-DD). Defaults to next week's same day.
        to_date: End date of the tour range (YYYY-MM-DD). Defaults to three days after from_date.
        config: Runtime configuration (automatically injected).

    Returns:
        A string containing the tour details in JSON format or an error message.
    """
    booking_url = TOUR_BOOKING_URL
    site_key = SITE_KEY

    headers = {
        "accept": "application/json",
        "siteKey": site_key,
        "Content-Type": "application/json",
    }

    # Calculate default dates if not provided
    default_from_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
    default_to_date = (datetime.now() + timedelta(days=10)).strftime("%Y-%m-%d")
    from_date = from_date if from_date else default_from_date
    to_date = to_date if to_date else default_to_date

    payload = {
        "serviceType": "TOUR",
        "date": {
            "fromDate": from_date,
            "toDate": to_date,
        },
        "filter": {},
        "extras": ["notFilter"],
        "Codes": [tour_code],
    }

    logging.info(f"Fetching details for tour {tour_code} from {from_date} to {to_date}")

    try:
        logging.info(f"Making API request to: {booking_url} with timeout: 5s")
        response = requests.post(booking_url, json=payload, headers=headers, timeout=5)

        # Log the response status and headers
        logging.info(f"Response status: {response.status_code}")
        logging.info(f"Response headers: {response.headers}")

        response.raise_for_status()  # Raise error for bad status

        # Try to parse the JSON response
        try:
            data = response.json()
            logging.info(f"Response data structure: {list(data.keys())}")

            if "result" in data and "items" in data["result"] and data["result"]["items"]:
                logging.info(f"Found details for tour {tour_code}")
                return json.dumps(data["result"]["items"])
            else:
                logging.warning(f"No tour details found in response: {data}")
                return "No tour details available."

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from tour API: {e}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error fetching tour details: {str(e)}")

@tool("booking_tour")
def booking_tour() -> str:
    """
    Provides a template for booking a tour after a user has selected one.

    Returns:
        A string containing instructions for confirming tour bookings.
    """
    return """
    Confirm the selected tour with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """