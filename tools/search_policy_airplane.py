from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import json
import logging
from langchain_core.tools import tool, ToolException
from langchain_tavily import TavilySearch
from utils.config import TAVILY_API_KEY


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AirplanePolicySearchInput(BaseModel):
    """Input schema for airplane policy search"""
    query: str = Field(description="The query to search for in airline policies")
    airline: Optional[str] = Field(
        default="bambooairways",
        description="Airline to search: 'vietnamairlines', 'vietjetair', 'pacificairlines', 'bambooairways', 'vietravelairlines'"
    )
    policy_type: Optional[str] = Field(
        default="all",
        description="Type of policy to search: 'baggage', 'fees', 'fare_rules', 'carriage', 'about', or 'all'"
    )
    search_depth: Optional[str] = Field(
        default="advanced",
        description="Search depth, either 'basic' (faster) or 'advanced' (more thorough)"
    )
    max_results: Optional[int] = Field(
        default=5,
        description="Maximum number of search results to return (1-10)"
    )


# Policy URL and context mapping for each airline
AIRLINE_POLICIES = {
    "vietnamairlines": {
        "domain": ["vietnamairlines.com"],
        "policies": {
            "fare_rules": "https://www.vietnamairlines.com/vn/vi/plan-book/book-flight-now/fare-types",
            "fees": [
                "https://www.vietnamairlines.com/vn/vi/buy-tickets-other-products/fare-conditions/taxes-fee-charges-surcharges/passenger-service-charges-security-screening-service-charges-and-value-added-tax",
                "https://www.vietnamairlines.com/vn/vi/buy-tickets-other-products/fare-conditions/taxes-fee-charges-surcharges/vietnam-airlines-surcharges"
            ],
            "carriage": [
                "https://www.vietnamairlines.com/vn/vi/legal/conditions-of-carriage/carriage-conditions-of-VNA-Vasco",
                "https://www.vietnamairlines.com/~/media/FilesDownload/dieu%20le%20van%20chuyen%20-%20-final-20jun24.pdf"
            ],
            "baggage": [
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/baggage-allowance-hand-baggage",
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/baggage-allowance-checked-baggage",
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/excess-baggage",
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/special-baggage",
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/restricted-baggage",
                "https://www.vietnamairlines.com/vn/vi/travel-information/baggage/baggage-supports"
            ],
            "about": [
                "https://www.vietnamairlines.com/vn/vi/travel-information/airports-transit/transit-information",
                "https://www.vietnamairlines.com/vn/vi/support/customer-service-plan"
            ]
        },
        "context": {
            "fare_rules": "điều kiện giá vé fare rules",
            "fees": "thuế phí lệ phí tax fee surcharge",
            "carriage": "điều lệ vận chuyển conditions carriage",
            "baggage": "hành lý baggage allowance",
            "about": "thông tin về vietnam airlines about us"
        },
        "policy_names": {
            "fare_rules": "Điều kiện giá vé",
            "fees": "Thuế, phí và phụ thu",
            "carriage": "Điều lệ vận chuyển",
            "baggage": "Hành lý",
            "about": "Thông tin khác"
        }
    },
    "vietjetair": {
        "domain": ["vietjetair.com"],
        "policies": {
            "carriage": "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/dieu-le-van-chuyen-1601835865384",
            "fare_rules": "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/dieu-kien-ve-1641466500765",
            "fees": "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/phi-va-le-phi-1578483039924",
            "baggage": "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/quy-dinh-hanh-ly-1578483259803",
            "about": [
                "https://evoucher.vietjetair.com/",
                "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/thong-tin-boi-thuong-1578483460118",
                "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/giay-to-tuy-than-1578483122906",
                "https://www.vietjetair.com/vi/pages/de-co-chuyen-bay-tot-dep-1578323501979/san-bay-va-nha-ga-quoc-te-1578483188498"
            ]
        },
        "context": {
            "fare_rules": "điều kiện vé fare rules",
            "fees": "phí lệ phí tax fee surcharge",
            "carriage": "điều lệ vận chuyển conditions carriage",
            "baggage": "quy định hành lý baggage allowance",
            "about": "thông tin vietjet air about us"
        },
        "policy_names": {
            "fare_rules": "Điều kiện vé",
            "fees": "Phí và lệ phí",
            "carriage": "Điều lệ vận chuyển",
            "baggage": "Quy định hành lý",
            "about": "Thông tin khác"
        }
    },
    "pacificairlines": {
        "domain": ["pacificairlines.com", "pacific-airlines.com", "vietair.com.vn"],
        "policies": {
            "carriage": "https://www.pacificairlines.com/dieu-le-van-chuyen/",
            "baggage": "https://www.pacific-airlines.com/quy-dinh-hanh-ly-ky-gui-va-xach-tay-cua-hang-pacific-airlines.html",
            "about": [
                "https://www.pacific-airlines.com/cac-dich-vu-dac-biet-cua-pacific-airlines-gom-co-nhung-gi.html",
                "https://www.pacificairlines.com/chinh-sach-boi-thuong/"
            ],
            "fees": "https://vietair.com.vn/tin-tuc/phi-va-le-phi-cua-hang-hang-khong-jetstar-pacific",
            "fare_rules": "https://www.pacific-airlines.com/cac-hang-ve-may-bay-cua-pacific-airlines.html"
        },
        "context": {
            "fare_rules": "điều kiện giá vé fare rules",
            "fees": "phí lệ phí tax fee surcharge",
            "carriage": "điều lệ vận chuyển conditions carriage",
            "baggage": "hành lý baggage allowance",
            "about": "thông tin pacific airlines about us"
        },
        "policy_names": {
            "fare_rules": "Các hạng vé máy bay",
            "fees": "Phí và lệ phí",
            "carriage": "Điều lệ vận chuyển",
            "baggage": "Điều kiện hành lý",
            "about": "Dịch vụ & Chính sách khác"
        }
    },
    "bambooairways": {
        "domain": ["bambooairways.com"],
        "policies": {
            "baggage": "https://www.bambooairways.com/vn/vi/travel-info/baggage-info/baggage-allowance",
            "fees": "https://www.bambooairways.com/gl/vi/book/booking-information/tax-fee-and-surcharge",
            "fare_rules": "https://bambooairways.com/sg/vi/book/booking-information/fare-rules",
            "carriage": "https://www.bambooairways.com/vn/vi/legal/conditions-of-carriage",
            "about": "https://www.bambooairways.com/vn/vi/bamboo-airways/about-us"
        },
        "context": {
            "baggage": "hành lý baggage allowance",
            "fees": "phí lệ phí tax fee surcharge",
            "fare_rules": "điều kiện giá vé fare rules",
            "carriage": "điều lệ vận chuyển conditions carriage",
            "about": "thông tin về bamboo airways about us"
        },
        "policy_names": {
            "baggage": "Điều kiện hành lý",
            "fees": "Phí và lệ phí",
            "fare_rules": "Điều kiện giá vé",
            "carriage": "Điều lệ vận chuyển",
            "about": "Thông tin về Bamboo Airways"
        }
    },
    "vietravelairlines": {
        "domain": ["vietravelairlines.com"],
        "policies": {
            "baggage": [
                "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/thong-tin-hanh-ly/hanh-ly-xach-tay",
                "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/thong-tin-hanh-ly/hanh-ly-ky-gui",
                "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/thong-tin-hanh-ly/hanh-ly-dac-biet",
                "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/thong-tin-hanh-ly/hanh-ly-nguy-hiem-bi-han-che-hoac-cam-chuyen-cho",
                "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/thong-tin-hanh-ly/hanh-ly-bat-thuong"
            ],
            "fees": "https://www.vietravelairlines.com/th/vi/thong-tin-dat-ve/dat-ve-may-bay-va-huong-dan/phi-va-le-phi",
            "fare_rules": "https://www.vietravelairlines.com/vn/vi/thong-tin-dat-ve/dat-ve-may-bay-va-huong-dan/dieu-kien-gia-ve",
            "carriage": "https://www.vietravelairlines.com/vn/vi/phap-ly/chinh-sach-thuong-mai-dieu-le-van-chuyen",
            "about": []
        },
        "context": {
            "baggage": "hành lý baggage allowance",
            "fees": "phí lệ phí tax fee surcharge",
            "fare_rules": "điều kiện giá vé fare rules",
            "carriage": "điều lệ vận chuyển conditions carriage",
            "about": "thông tin vietravel airlines about us"
        },
        "policy_names": {
            "baggage": "Điều kiện hành lý",
            "fees": "Phí và lệ phí",
            "fare_rules": "Điều kiện giá vé",
            "carriage": "Điều lệ vận chuyển",
            "about": "Thông tin khác"
        }
    }
}


@tool("search_policy_airplane", args_schema=AirplanePolicySearchInput)
def search_policy_airplane(
    query: str,
    airline: str = "bambooairways",
    policy_type: str = "all",
    search_depth: str = "advanced",
    max_results: int = 5,
) -> str:
    """
    Search airline policies for specific information using Tavily API.
    Args:
        query: The query to search for in airline policies.
        airline: Airline to search ('vietnamairlines', 'vietjetair', 'pacificairlines', 'bambooairways', 'vietravelairlines').
        policy_type: Type of policy to search ('baggage', 'fees', 'fare_rules', 'carriage', 'about', or 'all').
        search_depth: Search depth, either 'basic' (faster) or 'advanced' (more thorough).
        max_results: Maximum number of search results to return (1-10).
    Returns:
        A string containing the search results in JSON format or an error message.
    """
    api_key = TAVILY_API_KEY
    if not api_key:
        return "Error: Tavily API key is not configured."

    # Validate parameters
    if search_depth not in ["basic", "advanced"]:
        search_depth = "advanced"
    if max_results < 1:
        max_results = 1
    elif max_results > 3:
        max_results = 3

    airline = (airline or "bambooairways").lower()
    if airline not in AIRLINE_POLICIES:
        airline = "bambooairways"
    airline_info = AIRLINE_POLICIES[airline]

    valid_policy_types = list(airline_info["policies"].keys()) + ["all"]
    if policy_type not in valid_policy_types:
        policy_type = "all"

    # Build search query with airline context
    airline_query = f"site:{' OR site:'.join(airline_info['domain'])} {query}"
    if policy_type != "all":
        context = airline_info["context"].get(policy_type, "")
        airline_query += f" {context}"

    logger.info(f"Searching {airline} policies with query: {airline_query}")

    try:
        tavily_search = TavilySearch(
            max_results=max_results,
            topic="general",
            include_raw_content=True,
            include_images=False,
            search_depth=search_depth,
            include_domains=airline_info["domain"],
            api_key=api_key
        )
        search_results = tavily_search.invoke(airline_query)
        logger.info(f"Received {len(search_results.get('results', []))} search results from {airline}")
        formatted_results = []
        for result in search_results.get("results", []):
            result_url = result.get("url", "")
            detected_policy = "unknown"
            for policy, urls in airline_info["policies"].items():
                if not urls:
                    continue
                if isinstance(urls, str):
                    urls = [urls]
                for url in urls:
                    if policy in result_url.lower() or any(keyword in result_url.lower() for keyword in url.split("/")[-2:]):
                        detected_policy = policy
                        break
                if detected_policy != "unknown":
                    break
            formatted_result = {
                "title": result.get("title", "No title"),
                "url": result.get("url", "No URL"),
                "content": result.get("content", "No content"),
                "raw_content": result.get("raw_content", ""),
                "policy_type": detected_policy,
                "policy_name": airline_info["policy_names"].get(detected_policy, "Chính sách khác")
            }
            formatted_results.append(formatted_result)
        if policy_type != "all":
            formatted_results.sort(key=lambda x: 0 if x["policy_type"] == policy_type else 1)
        return json.dumps({
            "query": query,
            "airline": airline,
            "policy_type_requested": policy_type,
            "search_query_used": airline_query,
            "total_results": len(formatted_results),
            "results": formatted_results,
            "available_policies": airline_info["policy_names"]
        }, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Error during {airline} policy search: {str(e)}")
        raise ToolException(f"Error searching {airline} policies: {str(e)}")


# This block runs when the script is executed directly
if __name__ == "__main__":
    
    def test_policy_search():
        """Test the search_policy_airplane tool function directly"""
        print("\n=== Testing Bamboo Airways Policy Search Tool ===")

        # Get the underlying function from the tool decorator
        search_func = search_policy_airplane.func

        try:
            # Test 1: Search for baggage information
            print("\nTest 1: Baggage policy search")
            result = search_func("hành lý xách tay", airline="vietnamairlines")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Airline: {result_json.get('airline')}")
            print(f"Policy type: {result_json.get('policy_type_requested')}")
            print(f"Found {result_json.get('total_results')} results")

            # Test 2: Search for fees and charges
            print("\nTest 2: Fees and charges search")
            result = search_func("phí hủy vé", airline="vietnamairlines")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Airline: {result_json.get('airline')}")
            print(f"Found {result_json.get('total_results')} results")

            # Test 3: Search fare rules
            print("\nTest 3: Fare rules search")
            result = search_func("đổi vé", airline="vietnamairlines")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Airline: {result_json.get('airline')}")
            print(f"Found {result_json.get('total_results')} results")

            # Test 4: General search across all policies
            print("\nTest 4: General search across all policies")
            result = search_func("refund hoàn tiền", airline="vietnamairlines")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Airline: {result_json.get('airline')}")
            print(f"Found {result_json.get('total_results')} results")

            # Test 5: About airline
            print("\nTest 5: About airline")
            result = search_func("lịch sử công ty", airline="vietnamairlines")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Airline: {result_json.get('airline')}")
            print(f"Found {result_json.get('total_results')} results")

            return True
        except Exception as e:
            print(f"Error testing policy search tool: {str(e)}")
            return False

    print("Testing Bamboo Airways Policy Search...")
    test_policy_search()