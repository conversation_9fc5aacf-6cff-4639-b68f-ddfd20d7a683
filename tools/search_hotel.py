from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool, ToolException
from utils.config import HOTEL_SEARCH_URL, HOTEL_CORE_URL, SITE_KEY
import requests
import json
import logging
import re


# Define input schema for better type checking and documentation
class HotelSearchInput(BaseModel):
    """Input schema for hotel search"""
    chat_id: str = Field(description="The uuid associated with the user session")
    query: str = Field(description="The search query containing hotel preferences and location")

class HotelInfoInput(BaseModel):
    """Input schema for detailed hotel information"""
    hotel_id: int = Field(description="The HotelId of the hotel. Do Not Generated.")
    checkin_date: Optional[str] = Field(default="", description="Checkin date (YYYY-MM-DD)." )
    checkout_date: Optional[str] = Field(default="", description="Checkout date (YYYY-MM-DD).")

@tool("search_hotel", args_schema=HotelSearchInput)
def search_hotel(
    chat_id: str,
    query: str,
) -> str:
    """
    Search for hotels based on a user query. Use only for general HOTEL information queries (excluding price).

    Args:
        chat_id: The uuid associated with the user session.
        query: The search query containing hotel preferences and location.

    Returns:
        A string containing the search results in JSON format or an error message.
    """
    base_url = HOTEL_SEARCH_URL
    site_key = SITE_KEY

    headers = {
        "accept": "application/json",
        "siteKey": site_key,
        "Content-Type": "application/json",
    }
    payload = {"chat_id": chat_id, "query": query}

    logging.info(f"Hotel search query: {query} for chat_id: {chat_id}")

    try:
        logging.info(f"Making API request to: {base_url} with timeout: 5s")
        response = requests.post(base_url, json=payload, headers=headers, timeout=5)

        # Log the response status and headers
        logging.info(f"Response status: {response.status_code}")
        logging.info(f"Response headers: {response.headers}")

        response.raise_for_status()  # Raise error for bad status

        # Try to parse the JSON response
        try:
            data = response.json()
            logging.info(f"Response data structure: {list(data.keys())}")

            if "result" in data and "message" in data["result"] and data["result"]["message"]:
                text = json.dumps(data["result"]["message"])
                hotels = text.split("____________________")[1:]
                logging.info(f"Found {len(hotels)} hotels")
                scores = re.findall(r'PopularityScore:\s*(\d+)', text)
                top_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)[:5]
                result = "____________________".join([hotels[i] for i in top_indices])

            #     return result
                return f"RESULT: {text}. You must remember all HotelId and name of these hotels for later retrieval when using get_more_hotel_info."
            else:
                logging.warning(f"No hotels found in response: {data}")
                return "No hotels available."

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from hotel API: {e}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error searching for hotels: {str(e)}")

@tool("booking_hotel")
def booking_hotel() -> str:
    """
    Provides a template for booking a hotel after a user has selected one or mention a specific hotel name.

    Returns:
        A string containing instructions for confirming hotel bookings.
    """
    return """
    Confirm the selected hotel with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """

@tool("get_more_hotel_info", args_schema=HotelInfoInput)
def get_more_hotel_info(
    hotel_id: int,
    checkin_date: str = "",
    checkout_date: str = "",
) -> str:
    """
    Retrieve detailed information for a specific hotel.
    Use this for fetching hotel data by HotelId.

    Args:
        hotel_id: The HotelId of the hotel to retrieve information for. Do Not Generate This Argument.
        checkin_date: Hotel checkin date of customer (YYYY-MM-DD).
        checkout_date: Hotel checkout date of the hotel (YYYY-MM-DD).

    Returns:
        A string containing the hotel information in JSON format or an error message.
    """
    # Calculate default dates if not provided
    default_checkin_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
    default_checkout_date = (datetime.now() + timedelta(days=10)).strftime("%Y-%m-%d")
    checkin_date = checkin_date if checkin_date else default_checkin_date
    checkout_date = checkout_date if checkout_date else default_checkout_date

    
    url = f'{HOTEL_CORE_URL}v2/hotels/hotels'
    site_key = SITE_KEY
    
    payload = {
        'occupancy': {
            'adults': 1,
            'children': 0,
            'childAges': [],
            'numOfRoom': 1,
            'count': 1
        },
        'extras': ['notFilterPrice'],
        'page': 1,
        'pageSize': 20,
        'EnumOperationName': 5,
        'currency': 'VND',
        'source': 'tripgo',
        'hotels': [hotel_id],
        "checkinDT": checkin_date,
        "checkoutDT": checkout_date,
    }

    headers = {
        "accept": "application/json",
        "siteKey": site_key,
        "Content-Type": "application/json",
    }
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=5)
        response.raise_for_status()

        try:
            data = response.json()
            logging.info(f"Response data structure: {list(data.keys())}")

            if "result" in data and "items" in data["result"] and data["result"]["items"]:
                logging.info(f"Found details for hotels {hotel_id}")
                return json.dumps(data["result"]["items"])
            else:
                logging.warning(f"No hotel details found in response: {data}")
                return "No hotel details available."

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from hotel API: {e}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error searching for hotels: {str(e)}")

if __name__ == "__main__":

    # print(get_more_hotel_info.invoke({
    #     'hotel_id': 35535443, 
    #     'checkin_date': '2025-06-06', 
    #     'checkout_date': '2025-06-07'
    # }))

    print(search_hotel({
        'chat_id': '12323423',
        'query': "Khach san o Da Lat"
    }))