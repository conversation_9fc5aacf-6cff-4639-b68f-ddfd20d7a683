from pydantic import BaseModel, Field
import json
import logging
from langchain_core.tools import tool, ToolException
from tools.search_kb import make_request


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


knowledge_bases = ["d44d6816-fc4b-4c8f-aac2-59709df200fd", "e6472ef4-8b38-4647-bd63-22ebcaf9d731", "21e685e8-4bcb-4711-a0d9-c19b483ff6f0"]


class TicketSearchInput(BaseModel):
    """Input schema for searching tickets"""
    query: str = Field(description="Search query for the tickets")



@tool("search_ticket", args_schema=TicketSearchInput)
def search_ticket(
    query: str,
) -> str:
    """
    Search tickets.

    Args:
        query: Search query for the sightseeing tickets, for examples: <PERSON>pear<PERSON>, Vinwonders, HappyLand,...

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    data = {
        "collection_names": knowledge_bases,
        "query": query,
        "k": 5,
        "hybrid": False
    }
    logger.info(f"Searching tickets: {knowledge_bases} with query: {query}")

    try:
        result = make_request("POST", "/query/collection", data)
        if result:
            return json.dumps(result)
        return "No query results available."
    except ToolException as e:
        return str(e)


@tool("get_more_ticket_info")
def get_more_ticket_info(
    ticket_name: str,
) -> str:
    """
    Retrieve detailed information for a specific ticket.
    Use this for fetching ticket data by ticket name or attraction.

    Args:
        ticket_name: The ticket name or attraction to retrieve information for.

    Returns:
        A string containing the ticket information in JSON format or an error message.
    """
    return f"You could fetch your results from search_ticket tool to get more information about the ticket {{{ticket_name}}}. Return the result to supervisor after that."


@tool("booking_ticket")
def booking_ticket() -> str:
    """
    Provides a template for booking a ticket after a user has selected one.

    Returns:
        A string containing instructions for confirming ticket bookings.
    """
    return """
    Confirm the selected ticket with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """

