from pydantic import BaseModel, Field
from typing import List, Optional
import json
import logging
from langchain_core.tools import tool, ToolException
from tools.search_kb import make_request


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Knowledge base IDs
KNOWLEDGE_BASES = {
    "rovi_travel": "3dd79a00-70df-4d35-ad7a-e568c323d338",
    "vhi": "404122e5-58f6-4af8-b68e-b800a3ba280a",
    "teambuilding": "1237c178-2298-4cf6-bb7b-3abbe2ed8133",
    "gala_dinner": "6a767113-f87a-4944-9e7a-fc47a447671e"
}


class CompanyInfoSearchInput(BaseModel):
    """Input schema for searching company information"""
    query: str = Field(description="Search query for company information")
    category: Optional[str] = Field(
        default="all",
        description="Category to search in: 'rovi_travel', 'vhi', 'teambuilding', 'gala_dinner', or 'all'"
    )


@tool("search_company_info", args_schema=CompanyInfoSearchInput)
def search_company_info(
    query: str,
    category: str = "all"
) -> str:
    """
    Search for company information in knowledge bases.

    Args:
        query: Search query for company information.
        category: Category to search in: 'rovi_travel', 'vhi', 'teambuilding', 'gala_dinner', or 'all'.
               - rovi_travel: Information about Rovi Travel
               - vhi: Information about Vietnam Hydrogen Investment
               - teambuilding: Information about Teambuilding services
               - gala_dinner: Information about Gala Dinner concepts
               - all: Search across all categories

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    # Determine which knowledge bases to search
    if category.lower() == "all":
        collection_names = list(KNOWLEDGE_BASES.values())
    elif category.lower() in KNOWLEDGE_BASES:
        collection_names = [KNOWLEDGE_BASES[category.lower()]]
    else:
        return f"Error: Invalid category '{category}'. Valid categories are: {', '.join(list(KNOWLEDGE_BASES.keys()) + ['all'])}"

    data = {
        "collection_names": collection_names,
        "query": query,
        "k": 5,
        "k_reranker": 5,
        "r": 0.8,
        "hybrid": False
    }
    
    category_name = category if category.lower() != "all" else "all categories"
    logger.info(f"Searching {category_name} with query: {query}")

    try:
        result = make_request("POST", "/query/collection", data)
        if result:
            # Add category information to the results
            for item in result.get("results", []):
                kb_id = item.get("collection_name", "")
                for name, id_value in KNOWLEDGE_BASES.items():
                    if id_value == kb_id:
                        item["category"] = name
                        break
            
            return json.dumps(result)
        return f"No information available for {category_name}."
    except ToolException as e:
        return str(e)


class SpecificCompanyInfoInput(BaseModel):
    """Input schema for searching specific company information"""
    query: str = Field(description="Search query for company information")


@tool("search_rovi_travel", args_schema=SpecificCompanyInfoInput)
def search_rovi_travel(query: str) -> str:
    """
    Search for information about Rovi Travel.

    Args:
        query: Search query for Rovi Travel information.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    return search_company_info(query=query, category="rovi_travel")


@tool("search_vhi", args_schema=SpecificCompanyInfoInput)
def search_vhi(query: str) -> str:
    """
    Search for information about Vietnam Hydrogen Investment (VHI).

    Args:
        query: Search query for VHI information.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    return search_company_info(query=query, category="vhi")


@tool("search_teambuilding", args_schema=SpecificCompanyInfoInput)
def search_teambuilding(query: str) -> str:
    """
    Search for information about Teambuilding services.

    Args:
        query: Search query for Teambuilding information.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    return search_company_info(query=query, category="teambuilding")


@tool("search_gala_dinner", args_schema=SpecificCompanyInfoInput)
def search_gala_dinner(query: str) -> str:
    """
    Search for information about Gala Dinner concepts.

    Args:
        query: Search query for Gala Dinner information.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    return search_company_info(query=query, category="gala_dinner")