from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Dict, Any
import json
import logging
from langchain_core.tools import tool, ToolException
from langchain_tavily import TavilySearch
from utils.config import TAVILY_API_KEY


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebSearchInput(BaseModel):
    """Input schema for web search"""
    query: str = Field(description="The search query to look up on the web")
    search_depth: Optional[str] = Field(
        default="basic",
        description="Search depth, either 'basic' (faster) or 'advanced' (more thorough)"
    )
    include_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains to include in the search results"
    )
    exclude_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains to exclude from the search results"
    )
    max_results: Optional[int] = Field(
        default=5,
        description="Maximum number of search results to return (1-10)"
    )
    time_range: Optional[str] = Field(
        default=None,
        description="Time range for results: 'day', 'week', 'month', or 'year'"
    )


@tool("search_web", args_schema=WebSearchInput)
def search_web(
    query: str,
    search_depth: str = "basic",
    include_domains: Optional[List[str]] = None,
    exclude_domains: Optional[List[str]] = None,
    max_results: int = 5,
    time_range: Optional[str] = None,
) -> str:
    """
    Search the web for information using Tavily API.

    Args:
        query: The search query to look up on the web.
        search_depth: Search depth, either 'basic' (faster) or 'advanced' (more thorough).
        include_domains: List of domains to include in the search results.
        exclude_domains: List of domains to exclude from the search results.
        max_results: Maximum number of search results to return (1-10).
        time_range: Time range for results: 'day', 'week', 'month', or 'year'.

    Returns:
        A string containing the search results in JSON format or an error message.
    """
    api_key = TAVILY_API_KEY
    if not api_key:
        return "Error: Tavily API key is not configured."

    # Validate parameters
    if search_depth not in ["basic", "advanced"]:
        search_depth = "basic"

    if max_results < 1:
        max_results = 1
    elif max_results > 10:
        max_results = 10

    if time_range and time_range not in ["day", "week", "month", "year"]:
        time_range = None

    logger.info(f"Searching web with query: {query}")

    try:
        # Initialize the Tavily search tool with our parameters
        tavily_search = TavilySearch(
            max_results=max_results,
            topic="general",
            include_raw_content=False,
            include_images=True,
            search_depth=search_depth,
            time_range=time_range,
            include_domains=include_domains,
            exclude_domains=exclude_domains,
            api_key=api_key
        )

        # Invoke the search
        search_results = tavily_search.invoke(query)

        logger.info(f"Received {len(search_results.get('results', []))} search results")

        # Format the results for better readability
        formatted_results = []
        for result in search_results.get("results", []):
            formatted_result = {
                "title": result.get("title", "No title"),
                "url": result.get("url", "No URL"),
                "content": result.get("content", "No content"),
                "images": result.get("images", [])
            }
            formatted_results.append(formatted_result)

        return json.dumps({
            "query": search_results.get("query", query),
            "results": formatted_results
        }, indent=2)

    except Exception as e:
        logger.error(f"Error during web search: {str(e)}")
        raise ToolException(f"Error searching the web: {str(e)}")


# This block runs when the script is executed directly
if __name__ == "__main__":

    # Function to test the actual search_web tool function
    def test_search_web_tool():
        """Test the search_web tool function directly"""
        print("\n=== Testing the actual search_web tool function ===")

        # Get the underlying function from the tool decorator
        # This is necessary because the @tool decorator wraps the original function
        search_web_func = search_web.func

        try:
            # Test 1: Basic search
            print("\nTest 1: Basic search")
            result = search_web_func("Python programming best practices")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 2: Search with advanced depth
            print("\nTest 2: Advanced search depth")
            result = search_web_func("Machine learning frameworks", search_depth="advanced")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 3: Search with max results
            print("\nTest 3: Limited results (2)")
            result = search_web_func("Climate change news", max_results=2)
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 4: Search with time range
            print("\nTest 4: Time range (week)")
            result = search_web_func("Breaking news", time_range="week")
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 5: Search with domain inclusion
            print("\nTest 5: Include domains")
            result = search_web_func(
                "Technology reviews",
                include_domains=["techcrunch.com", "wired.com"]
            )
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 6: Search with domain exclusion
            print("\nTest 6: Exclude domains")
            result = search_web_func(
                "Smartphone comparison",
                exclude_domains=["amazon.com", "ebay.com"]
            )
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            # Test 7: Combined parameters
            print("\nTest 7: Combined parameters")
            result = search_web_func(
                "Electric vehicles",
                search_depth="advanced",
                max_results=3,
                time_range="month",
                include_domains=["caranddriver.com", "motortrend.com"]
            )
            result_json = json.loads(result)
            print(f"Query: {result_json.get('query')}")
            print(f"Found {len(result_json.get('results', []))} results")

            return True
        except Exception as e:
            print(f"Error testing search_web tool: {str(e)}")
            return False

    print("Testing Tavily search integration...")

    # Example 1: Basic search using the helper function
    print("\n=== Example 1: Basic search ===")
    _run_search_test("latest AI developments")

    # Example 2: Search with time range
    print("\n=== Example 2: Search with time range ===")
    _run_search_test("latest AI developments", time_range="week")

    # Example 3: Search with domain filtering
    print("\n=== Example 3: Search with domain filtering ===")
    _run_search_test("travel tips", include_domains=["lonelyplanet.com", "tripadvisor.com"])

    # Example 4: Exclude certain domains from search results
    print("\n=== Example 4: Exclude domains ===")
    _run_search_test("best smartphones", exclude_domains=["amazon.com"])

    # Test the actual search_web tool function
    test_search_web_tool()
