from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict
import requests
import json
import logging
from langchain_core.tools import tool, ToolException
from utils.config import RETRIEVAL_API_URL, AI_ROVI_TRAVEL_TOKEN, KNOWLEDGE_BASE_API


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryDocInput(BaseModel):
    """Input schema for querying a single document collection"""
    collection_name: str = Field(description="Name of the collection to query")
    query: str = Field(description="Search query for the document")
    r: Optional[float] = Field(default=0.8, description="Relevance threshold for results")
    hybrid: Optional[bool] = Field(default=False, description="Whether to use hybrid search")

class QueryCollectionsInput(BaseModel):
    """Input schema for querying multiple collections"""
    # collection_names: List[str] = Field(description="List of collection names to query")
    query: str = Field(description="Search query for the collections")
    r: Optional[float] = Field(default=0.8, description="Relevance threshold for results")
    hybrid: Optional[bool] = Field(default=False, description="Whether to use hybrid search")

def make_request(
    method: str,
    endpoint: str,
    data: Optional[Dict] = None,
    base_url: str = RETRIEVAL_API_URL,
    headers: Optional[Dict] = None
) -> Dict:
    """Helper function to make API requests"""
    url = f"{base_url.rstrip('/')}{endpoint}"
    if headers is None:
        headers = {
            "Authorization": f"Bearer {AI_ROVI_TRAVEL_TOKEN}",
            "Content-Type": "application/json",
            "accept": "application/json"
        }

    logger.info(f"Making {method} request to: {url}")

    try:
        response = requests.request(method, url, headers=headers, json=data)

        # Log the response status and headers
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response headers: {response.headers}")

        response.raise_for_status()

        # Handle empty response
        if not response.text:
            logger.warning("Empty response received")
            return {}

        # Try to parse the JSON response
        try:
            data = response.json()
            return data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from retrieval API: {e}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error making request to retrieval API: {str(e)}")

def get_all_knowledge() -> List[str]:
    """
    Get information of all Knowledge Base.
    """
    result = make_request("GET", "/list", {}, base_url=KNOWLEDGE_BASE_API)
    # return [(kb["id"], kb["name"]) for kb in result]
    return [kb for kb in result]

@tool("query_doc", args_schema=QueryDocInput)
def query_doc(
    collection_name: str,
    query: str,
    r: Optional[float] = 0.8,
    hybrid: Optional[bool] = False
) -> str:
    """
    Query a single document collection.

    Args:
        collection_name: Name of the collection to query.
        query: Search query for the document.
        r: Relevance threshold for results (default: 0.8).
        hybrid: Whether to use hybrid search (default: False).

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    data = {
        "collection_name": collection_name,
        "query": query,
        "k": 5,
        "r": r,
        "hybrid": hybrid
    }

    logger.info(f"Querying collection: {collection_name} with query: {query}")

    try:
        result = make_request("POST", "/query/doc", data)
        if result:
            return json.dumps(result)
        return "No query results available."
    except ToolException as e:
        return str(e)

@tool("query_collections", args_schema=QueryCollectionsInput)
def query_collections(
    query: str,
    # collection_names: List[str] = knowledge_bases,
    r: Optional[float] = 0.8,
    hybrid: Optional[bool] = False
) -> str:
    """
    Query multiple collections.

    Args:
        collection_names: List of collection names to query.
        query: Search query for the collections.
        r: Relevance threshold for results (default: 0.8).
        hybrid: Whether to use hybrid search (default: False).

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    knowledge_bases = get_all_knowledge()

    data = {
        "collection_names": knowledge_bases,
        "query": query,
        "k": 5,
        "r": r,
        "hybrid": hybrid
    }
    logger.info(f"Querying collections: {knowledge_bases} with query: {query}")

    try:
        result = make_request("POST", "/query/collection", data)
        if result:
            return json.dumps(result)
        return "No query results available."
    except ToolException as e:
        return str(e)


if __name__ == "__main__":
    print(get_all_knowledge())
