from pydantic import BaseModel, Field
from typing import Optional
import requests
import json
import logging
from langchain_core.tools import tool, ToolException
from utils import config
import pytz
from datetime import datetime

# Define input schema for the new flight info search
def _default_adults():
    return 1
def _default_children():
    return 0
def _default_infants():
    return 0

class FlightInfoSearchInput(BaseModel):
    """Input schema for detailed flight info search"""
    from_city_code: str = Field(description="IATA code of the departure city (e.g., 'HAN')")
    to_city_code: str = Field(description="IATA code of the destination city (e.g., 'SGN')")
    depart_date: str = Field(description="Departure date in format YYYY/MM/DD (e.g., '2025/05/31')")
    total_adults: int = Field(default_factory=_default_adults, description="Number of adults (default 1)")
    total_children: int = Field(default_factory=_default_children, description="Number of children (default 0)")
    total_infants: int = Field(default_factory=_default_infants, description="Number of infants (default 0)")

@tool("search_flight_info", args_schema=FlightInfoSearchInput)
def search_flight_info(
    from_city_code: str,
    to_city_code: str,
    depart_date: str,
    total_adults: int = 1,
    total_children: int = 0,
    total_infants: int = 0,
) -> str:
    """
    Search for available flights using the provided API endpoint and headers.
    Args:
        from_city_code: IATA code of the departure city (e.g., 'HAN').
        to_city_code: IATA code of the destination city (e.g., 'SGN').
        depart_date: Departure date in format YYYY/MM/DD (e.g., '2025/05/31').
        total_adults: Number of adults (default 1).
        total_children: Number of children (default 0).
        total_infants: Number of infants (default 0).
    Returns:
        A string containing the search results in JSON format or an error message.
    """
    url = config.FLIGHT_API_DETAIL_URL
    if not url or url == "None":
        raise ToolException("Error: FLIGHT_API_DETAIL_URL is not set. Please check your .env or environment variables.")
    if not config.FLIGHT_API_TOKEN or config.FLIGHT_API_TOKEN == "None":
        raise ToolException("Error: FLIGHT_API_TOKEN is not set. Please check your .env or environment variables.")
    if not config.SITE_KEY or config.SITE_KEY == "None":
        raise ToolException("Error: DEV_SITE_KEY is not set. Please check your .env or environment variables.")
    headers = {
        "accept": "application/json",
        "authorization": f"Bearer {config.FLIGHT_API_TOKEN}",
        "sitekey": config.SITE_KEY 
    }
    params = {
        "fromCityCode": from_city_code,
        "toCityCode": to_city_code,
        "departDate": depart_date,
        "totalAdults": total_adults,
        "totalChildren": total_children,
        "totalInfants": total_infants,
    }
    logging.info(f"Flight info search from {from_city_code} to {to_city_code} on {depart_date}, adults: {total_adults}, children: {total_children}, infants: {total_infants}")
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        logging.info(f"Response status: {response.status_code}")
        logging.info(f"Response headers: {response.headers}")
        response.raise_for_status()
        try:
            data = response.json()
            # NEW: Handle case with result.airAvailabilities[].trips[]
            if (
                isinstance(data, dict)
                and "result" in data
                and isinstance(data["result"], dict)
                and "airAvailabilities" in data["result"]
                and isinstance(data["result"]["airAvailabilities"], list)
            ):
                trips = []
                for avail in data["result"]["airAvailabilities"]:
                    if isinstance(avail, dict) and "trips" in avail and isinstance(avail["trips"], list):
                        trips.extend(avail["trips"])
                trips = trips[:10]
                return json.dumps(trips)
            # Limit results to 10 if data is a list or contains a list field
            if isinstance(data, list):
                data = data[:10]
                data = [_shorten_ticket(ticket) for ticket in data]
                return json.dumps(data)
            elif isinstance(data, dict):
                # Try common keys that may contain lists
                for key in [
                    "data", "tickets", "items", "result", "listTicketPlans", "listTicketPrices", "listTicketPassengers"
                ]:
                    if key in data and isinstance(data[key], list):
                        data[key] = [_shorten_ticket(ticket) for ticket in data[key][:10]]
                # Also handle nested dicts (e.g., data['result']['items'])
                if "result" in data and isinstance(data["result"], dict):
                    for subkey in ["items", "listTicketPlans", "listTicketPrices", "listTicketPassengers"]:
                        if subkey in data["result"] and isinstance(data["result"][subkey], list):
                            data["result"][subkey] = [_shorten_ticket(ticket) for ticket in data["result"][subkey][:10]]
                return json.dumps(data)
            else:
                return json.dumps(data)
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from flight info API: {e}")
    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error searching for flight info: {str(e)}")

def _shorten_ticket(ticket):
    price = ticket.get("priceValue") or ticket.get("soldPrice")
    # Extract and format departure/arrival time from segments if available
    departure_time = None
    arrival_time = None
    segments = ticket.get("segments")
    if segments and isinstance(segments, list) and len(segments) > 0:
        dep = segments[0].get("departure", {})
        arr = segments[0].get("arrival", {})
        # Convert epoch to UTC time
        try:
            if dep.get("atTime"):
                dt = datetime.utcfromtimestamp(dep["atTime"])
                departure_time = dt.strftime("%y/%m/%d %H:%M")
            if arr.get("atTime"):
                at = datetime.utcfromtimestamp(arr["atTime"])
                arrival_time = at.strftime("%y/%m/%d %H:%M")
        except Exception:
            pass
    return {
        "flightNo": ticket.get("flightNo"),
        "fromAirportCode": ticket.get("fromAirportCode"),
        "toAirportCode": ticket.get("toAirportCode"),
        "startDate": ticket.get("startDate"),
        "endDate": ticket.get("endDate"),
        "ticketClass": ticket.get("ticketClass"),
        "airlineName": ticket.get("airlineName"),
        "price": price,
        "duration": ticket.get("duration"),  # if available
        "stops": ticket.get("stops"),        # if available
        "baggage": ticket.get("baggage"),    # if available
        "details": ticket.get("details"),    # any other important info
        "departureTime": departure_time,
        "arrivalTime": arrival_time,
    } 