from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any, Literal
import requests
import json
import logging
from datetime import datetime
from langchain_core.tools import tool, ToolException
from utils.config import ODOO_BASE_URL, ODOO_DATABASE_NAME, ODOO_USER_NAME, ODOO_PASSWORD

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define valid tag types
TagType = Literal["Combo", "Tour", "Hotel", "Ticket", "Flight", "eSIM", "VISA"]

# Tag ID mapping for different service types
TAG_IDS = {
    "Hotel": 9,
    "Tour": 10,
    "Ticket": 11,
    "Flight": 12,
    "eSIM": 13,
    "VISA": 14,
    "Combo": 15,
}

# Define input schemas for better type checking and documentation
class ContactInput(BaseModel):
    """Input schema for creating a contact"""
    name: str = <PERSON>(description="Name of the contact")
    phone: str = Field(description="Phone number of the contact")
    email: Optional[str] = Field(default="", description="Email address of the contact")

class LeadInput(BaseModel):
    """Input schema for creating a lead"""
    partner_name: str = Field(description="Customer name")
    email: Optional[str] = Field(default="", description="Customer email (optional)")
    phone: str = Field(description="Customer phone number")
    description: str = Field(description="Description of a Lead, summarize customer request.")
    source_id: int = Field(description="Source ID of the lead")
    tag_type: List[TagType] = Field(default=["Hotel"], description="List of tag types: Combo, Tour, Hotel, Ticket, Flight, eSIM, VISA")
    user_id: Optional[int] = Field(default=8, description="ID of the salesperson assigned to this lead")

class UpdateLeadInput(BaseModel):
    """Input schema for updating a lead"""
    lead_id: int = Field(description="Unique ID of the lead to update")
    description: Optional[str] = Field(default=None, description="New description or note for the lead, summarize all modifications.")
    email: Optional[str] = Field(default=None, description="New email address for the lead")
    phone: Optional[str] = Field(default=None, description="New phone number for the lead")
    name: Optional[str] = Field(default=None, description="New name for the lead")
    partner_name: Optional[str] = Field(default=None, description="New partner name")
    tag_type: Optional[List[TagType]] = Field(default=None, description="New tag types for the lead")

class ReconfirmInput(BaseModel):
    """Input schema for reconfirm customer request."""
    phone_number: str = Field(description="Phone number that customer provided.")


def create_jsonrpc_payload(method: str, params: Dict[str, Any], request_id: int = 1) -> Dict[str, Any]:
    """Create a standard JSON-RPC 2.0 payload."""
    return {
        "jsonrpc": "2.0",
        "method": "call",
        "params": params,
        "id": request_id,
    }

def create_execute_kw_params(db: str, uid: int, password: str, model: str, method: str, args: List[Any], tag_type: str = "Hotel") -> Dict[str, Any]:
    """Create parameters for execute_kw calls."""
    return {
        "service": "object",
        "method": "execute_kw",
        "args": [db, uid, password, model, method, args],
    }

def post_request(base_url: str, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    """Send a POST request to the Odoo API endpoint."""
    url = f"{base_url.rstrip('/')}{endpoint}"
    logger.info(f"Making POST request to: {url} with timeout: 5s")

    try:
        response = requests.post(url, json=payload, headers={"accept": "application/json"}, timeout=5)
        response.raise_for_status()

        # Try to parse the JSON response
        try:
            data = response.json()
            return data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response content: {response.text[:500]}...")
            raise ToolException(f"Error: Invalid response format from Odoo API: {e}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error making request to Odoo API: {str(e)}")

def authenticate() -> Optional[int]:
    """Authenticate with Odoo and return the user ID."""
    params = {
        "db": ODOO_DATABASE_NAME,
        "login": ODOO_USER_NAME,
        "password": ODOO_PASSWORD
    }
    payload = create_jsonrpc_payload("call", params)

    try:
        result = post_request(ODOO_BASE_URL, "/web/session/authenticate", payload)
        uid = result.get("result", {}).get("uid")
        if not uid:
            logger.error("Authentication failed: No user ID returned")
            raise ToolException("Authentication failed: No user ID returned")
        logger.info(f"Authenticated successfully with user ID: {uid}")
        return uid
    except ToolException as e:
        logger.error(f"Authentication error: {str(e)}")
        raise

def contact_exists(email: str = "", phone: str = "") -> Optional[int]:
    """Check if a contact exists by email or phone and return its ID."""
    uid = authenticate()
    if not uid:
        logger.error("Authentication failed. Cannot check contact existence.")
        raise ToolException("Authentication failed. Cannot check contact existence.")

    domain: List[Any] = []
    if email:
        domain.append(("email", "=", email))
    if phone:
        domain.append(("phone", "=", phone))

    params = create_execute_kw_params(ODOO_DATABASE_NAME, uid, ODOO_PASSWORD, "res.partner", "search", [domain])
    payload = create_jsonrpc_payload("call", params, 2)

    try:
        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
        contacts = result.get("result", [])

        if isinstance(contacts, list) and contacts:
            logger.info(f"Found existing contact with ID: {contacts[0]}")
            return contacts[0]

        logger.info("No existing contact found")
        return None

    except ToolException as e:
        logger.error(f"Error checking contact existence: {str(e)}")
        raise

def create_contact(
    name: str,
    phone: str,
    email: str = "",
) -> str:
    """
    Create a new contact or return existing contact ID.

    Args:
        name: Name of the contact.
        phone: Phone number of the contact.
        email: Email address of the contact (optional).

    Returns:
        A string containing the contact ID or an error message.
    """
    logger.info(f"Creating or retrieving contact: {name}, phone: {phone}, email: {email}")

    try:
        contact_id = contact_exists(email, phone)
        if contact_id:
            return f"Contact already exists with ID: {contact_id}"

        uid = authenticate()
        params = create_execute_kw_params(
            ODOO_DATABASE_NAME,
            uid,
            ODOO_PASSWORD,
            "res.partner",
            "create",
            [{"name": name, "email": email, "phone": phone}]
        )
        payload = create_jsonrpc_payload("call", params, 3)

        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
        contact_id = result.get("result")

        if contact_id:
            logger.info(f"Created new contact with ID: {contact_id}")
            return f"Contact created successfully with ID: {contact_id}"
        else:
            logger.error("Failed to create contact")
            return "Failed to create contact"

    except ToolException as e:
        return f"Error creating contact: {str(e)}"

@tool("create_lead", args_schema=LeadInput)
def create_lead(
    partner_name: str,
    phone: str,
    description: str,
    source_id: int,
    email: Optional[str] = "",
    tag_type: List[TagType] = ["Hotel"],
    user_id: Optional[int] = 8,
) -> str:
    """
    Create a new lead in Odoo, ensuring the contact exists first.

    CRITICAL: This tool requires BOTH partner_name and phone to be provided and not empty.
    Do not create leads with missing or placeholder information.

    Args:
        partner_name: Customer name (REQUIRED - must be real customer name, not placeholder).
        phone: Customer phone number (REQUIRED - must be real phone number, not placeholder).
        description: Lead description.
        source_id: Source ID of the lead.
        email: Customer email (optional).
        tag_type: Type of tag to use (Combo, Tour, Hotel, Ticket, Flight, eSIM, VISA). Defaults to Hotel.
        user_id: ID of the salesperson assigned to this lead.

    Returns:
        A string containing a success message with the lead ID or an error message.
    """
    logger.info(f"Creating lead for {partner_name}, tag_type: {tag_type}")

    try:
        # CRITICAL VALIDATION: Check if both name and phone are provided and not empty
        if not partner_name or not partner_name.strip():
            logger.error("Lead creation failed: Partner name is missing or empty")
            return "TELL THE USER: Cannot create lead without customer name. Please provide your name first."

        if not phone or not phone.strip():
            logger.error("Lead creation failed: Phone number is missing or empty")
            return "TELL THE USER: Cannot create lead without phone number. Please provide your phone number first."

        # Create or retrieve contact
        contact_result = create_contact(partner_name, phone, email)
        contact_id = None
        if "Contact created successfully with ID" in contact_result or "Contact already exists with ID" in contact_result:
            contact_id = int(contact_result.split(": ")[-1])
        if not contact_id:
            logger.error("Failed to create or retrieve contact")
            return "Failed to create or retrieve contact"

        # Validate tag_type
        valid_tag_ids = []
        for tag in tag_type:
            if tag in TAG_IDS:
                valid_tag_ids.append(TAG_IDS[tag])
            else:
                logger.warning(f"Invalid tag_type detected and skipped: {tag}")
        if not valid_tag_ids:
            valid_tag_ids = [TAG_IDS["Hotel"]]


        # Prepare lead data
        lead_data = {
            "name": description,
            "partner_id": contact_id,
            "email_from": email,
            "phone": phone,
            "description": description,
            "source_id": source_id,
            "tag_ids": [[6, 0, valid_tag_ids]],
            "stage_id": 1,
        }
        if user_id is not None:
            lead_data["user_id"] = user_id

        uid = authenticate()
        params = create_execute_kw_params(
            ODOO_DATABASE_NAME,
            uid,
            ODOO_PASSWORD,
            "crm.lead",
            "create",
            [lead_data]
        )
        payload = create_jsonrpc_payload("call", params, 4)

        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
        lead_id = result.get("result")

        if lead_id:
            logger.info(f"Lead created successfully with ID: {lead_id}")
            return f"Remember the Lead ID: {lead_id} for future reference. Notify the user that the Lead has been successfully. Never provide lead ID to user."
        else:
            logger.error("Failed to create lead")
            return "Tell the user: 'Failed to create lead.'"

    except ToolException as e:
        logger.error(f"Error creating lead: {str(e)}")
        return f"Tell the user: 'Failed to create lead. Error: {str(e)}'"


def get_contact_id_from_lead(lead_id: int, uid) -> int:
    """
    Retrieve the contact ID (partner_id) from a given lead ID.

    Args:
        lead_id: ID of the lead.

    Returns:
        A string with the contact ID or an error message.
    """
    logger.info(f"Retrieving contact ID from lead ID: {lead_id}")
    
    try:
        params = create_execute_kw_params(
            ODOO_DATABASE_NAME,
            uid,
            ODOO_PASSWORD,
            "crm.lead",
            "read",
            [lead_id],
            {"fields": ["partner_id"]}
        )
        payload = create_jsonrpc_payload("call", params, 6)
        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)

        records = result.get("result")
        if records and records[0]["partner_id"]:
            return records[0]["partner_id"][0]

        else:
            logger.warning(f"No partner linked to lead ID: {lead_id}")
            return -1

    except ToolException as e:
        logger.error(f"Error retrieving contact from lead: {str(e)}")
        return f"Error retrieving contact from lead: {str(e)}"


def update_contact(
    contact_id: int,
    name: Optional[str] = None,
    phone: Optional[str] = None,
    email: Optional[str] = None
) -> None:
    """
    Update an existing contact's details.

    Args:
        contact_id: ID of the contact to update.
        name: New name of the contact (optional).
        phone: New phone number of the contact (optional).
        email: New email address of the contact (optional).

    Returns:
        A string indicating the result of the update operation.
    """
    logger.info(f"Updating contact ID: {contact_id} with name: {name}, phone: {phone}, email: {email}")

    try:
        uid = authenticate()

        # Tạo dictionary các trường cần cập nhật
        values = {}
        if name:
            values["name"] = name
        if phone:
            values["phone"] = phone
        if email:
            values["email"] = email

        if not values:
            logger.warning("No update values provided.")
            return "No fields to update"

        params = create_execute_kw_params(
            ODOO_DATABASE_NAME,
            uid,
            ODOO_PASSWORD,
            "res.partner",
            "write",
            [[contact_id], values]
        )

        payload = create_jsonrpc_payload("call", params, 7)

        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
        success = result.get("result")

    except ToolException as e:
        return f"Error updating contact: {str(e)}"


@tool("update_lead", args_schema=UpdateLeadInput)
def update_lead(
    lead_id: int,
    description: Optional[str] = None,
    email: Optional[str] = None,
    phone: Optional[str] = None,
    name: Optional[str] = None,
    partner_name: Optional[str] = None,
    tag_type: Optional[List[TagType]] = None,
) -> str:
    """
    Update information of an existing lead in Odoo CRM.

    Args:
        lead_id: Unique ID of the lead to update.
        description: New description or note for the lead (optional).
        email: New email address for the lead (optional).
        phone: New phone number for the lead (optional).
        name: New name for the lead (optional).
        partner_name: New name for the partner (optional).
        tag_type: New tag type for the lead (optional).

    Returns:
        A string message indicating the result of the update operation.
    """
    logger.info(f"Updating lead ID: {lead_id}")

    try:
        if not isinstance(lead_id, int):
            logger.error("Invalid lead_id: must be an integer")
            return "TELL THE USER: Failed to update lead. Invalid lead ID."

        # Check if at least one field to update is provided
        if not any([description, email, phone, name, tag_type, partner_name]):
            logger.error("No update fields provided")
            return "TELL THE USER: Failed to update lead. No update fields provided."

        uid = authenticate()

        if partner_name or email or phone:
            contact_id = get_contact_id_from_lead(lead_id, uid)
            update_contact(contact_id, email=email, phone=phone, name=partner_name)

        updates = {}

        # Add fields to update if they are provided
        if description:
            # First, get the current description
            get_lead_params = create_execute_kw_params(
                ODOO_DATABASE_NAME,
                uid,
                ODOO_PASSWORD,
                "crm.lead",
                "read",
                [[lead_id], ["description"]]
            )
            get_lead_payload = create_jsonrpc_payload("call", get_lead_params, 8)
            lead_result = post_request(ODOO_BASE_URL, "/jsonrpc", get_lead_payload)

            current_description = lead_result.get("result", [{}])[0].get("description", "")

            # Format new update and append to existing description
            now = datetime.now()
            formatted_update = f"<p>Update {now.strftime('%Y-%m-%d %H:%M:%S')} {description}</p>"
            updates["description"] = current_description + formatted_update

        if partner_name:
            updates["contact_name"] = partner_name

        if name:
            updates["name"] = name

        if tag_type:
            valid_tag_ids = []
            for tag in tag_type:
                if tag in TAG_IDS:
                    valid_tag_ids.append(TAG_IDS[tag])
                else:
                    logger.warning(f"Invalid tag_type detected and skipped: {tag}")

            if not valid_tag_ids:
                valid_tag_ids = [TAG_IDS["Hotel"]]

            updates["tag_ids"] = [[6, 0, valid_tag_ids]]

        params = create_execute_kw_params(
            ODOO_DATABASE_NAME,
            uid,
            ODOO_PASSWORD,
            "crm.lead",
            "write",
            [[lead_id], updates]
        )
        payload = create_jsonrpc_payload("call", params, 9)

        result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
        if result.get("result"):
            # Log what was updated
            updated_fields = ", ".join(updates.keys())
            logger.info(f"Lead ID: {lead_id} updated successfully. Updated fields: {updated_fields}")
            return f"TELL THE USER: Lead has been successfully updated!"
        else:
            logger.error(f"Failed to update lead ID: {lead_id}")
            return f"TELL THE USER: Failed to update lead."

    except ToolException as e:
        logger.error(f"Error updating lead: {str(e)}")
        return f"TELL THE USER: Failed to update lead. Error: {str(e)}"


def is_phone_number(phone_number: str) -> int:
    """ 
    Check phone number valid.
    """
    import re
    rule = re.compile(r'^(?:\+?44)?[07]\d{9,13}$')

    if not rule.search(phone_number):
        return 0
    return 1

@tool("reconfirm", args_schema=ReconfirmInput)
def reconfirm(phone_number: str) -> str:
    """
    Use this tool when customer have choose one of the provided options. (eg: Booking Ladalat hotel for 3 days 2 night, Vietnam Airlines flight from HN to SG,...) and has provided enough information for booking.

    Booking info required:
    - Customer's full name (REQUIRED - not placeholder)
    - Customer's phone number (REQUIRED - not placeholder)
    - Email address (optional but recommended)
    - Option that customer want to book (eg: Hotel, Flight, Tour, Ticket, eSIM, VISA)
    - Service details (eg: Ladalat hotel for 3 days 2 night, Vietnam Airlines flight from HN to SG,...)
    - Dates/timing if applicable
    - Number of people if applicable

    CRITICAL: Only use this tool if you have BOTH customer name AND phone number AND option that customer want to book. This tool is used for specific booking request.

    Returns:
        A string containing instructions for confirming service bookings.
    """
    if not is_phone_number(phone_number):
        return "Invalid Phone Number. You MUST ask cumstomer to provide their real phone number."

    return """
    IF customer did not provide you any option that they interest in 3 options you have provided:
        - You MUST REMIND them to choose (customers have to choose by themselves not by yourself)

    ESLE IF Any of the VALIDATION CHECKLIST is missing or unclear or customer not select a specific option:
        <VALIDATION CHECKLIST>
            ✓ Customer's full name is provided (not placeholder)
            ✓ Customer's phone number is provided (not placeholder)
            ✓ Service option and details are clear and specific (eg: Ladalat hotel for 3 days 2 night, Vietnam Airlines M212 from HN to SG,...):
        </VALIDATION CHECKLIST>

        - You must REQUEST customer to provide the missing details.

    ESLE:
        - Reconfirm with RECONFIRMATION FORMAT:

        <RECONFIRMATION FORMAT>
            Xin chào [Tên khách hàng], em xác nhận lại thông tin đặt dịch vụ của anh/chị:

            👤 Họ tên: [Customer Name]
            📞 Số điện thoại: [Phone Number]
            📧 Email: [Email if provided, if not could be omit and not display if not provided]
            🎯 Dịch vụ: [Service option and details]
            🎯 Option: [Option that customer selected in 3 options you have provided]
            📅 Thời gian: [Dates/timing if applicable]
            👥 Số người: [Number of people if applicable]

            Anh/chị vui lòng xác nhận thông tin trên có chính xác không? Em sẽ liên hệ bộ phận Dịch vụ bên em để hỗ trợ anh/chị ngay sau khi anh/chị xác nhận.

        </RECONFIRMATION FORMAT>
        - Wait for customer confirmation before proceeding to create_lead.
        - Only create lead AFTER customer explicitly confirms the information.
        - DO NOT create lead with missing or placeholder information.
    """


if __name__ == "__main__":
    def get_all_users() -> str:
        """
        Get all users from Odoo.

        Args:
        Returns:
            A string containing the list of users in JSON format or an error message.
        """
        logger.info("Fetching all users from Odoo")

        try:
            uid = authenticate()
            fields = ["id", "name", "login", "email", "active", "groups_id"]
            domain = []

            params = create_execute_kw_params(
                ODOO_DATABASE_NAME,
                uid,
                ODOO_PASSWORD,
                "res.users",
                "search_read",
                [domain, fields],
                "Hotel"
            )
            payload = create_jsonrpc_payload("call", params, 5)

            result = post_request(ODOO_BASE_URL, "/jsonrpc", payload)
            users = result.get("result", [])

            if users:
                logger.info(f"Found {len(users)} users")
                return json.dumps(users)
            else:
                logger.warning("No users found")
                return "No users found"

        except ToolException as e:
            return f"Error fetching users: {str(e)}"

    print(get_all_users())
    print(create_lead.invoke(
        {
            "partner_name": "Dung Partner",
            "phone": "098456789",
            "description": "Test Description",
            "source_id": 1,
            "email": "<EMAIL>",
            "tag_type": ["Hotel", "Flight"],
            "user_id": 8
        }
    ))

    print(update_lead.invoke(
        {
            "lead_id": 193,
            "description": "Test Description",
            "email": "<EMAIL>",
            "phone": "0943456789",
            "partner_name" : "Huynh Viet Dung",
            "tag_type" : ["eSIM", "VISA", "Tour", "Hotel"]
        }
    ))


