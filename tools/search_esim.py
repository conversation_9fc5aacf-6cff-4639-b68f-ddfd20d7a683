from pydantic import BaseModel, Field
import json
import logging
from langchain_core.tools import tool, ToolException
from tools.search_kb import make_request


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# eSIM knowledge base ID
esim_knowledge_base = "885ccfc9-254b-41c4-a726-03f37d77deab"


class EsimSearchInput(BaseModel):
    """Input schema for searching eSIM information"""
    query: str = Field(description="Search query for eSIM information")


@tool("search_esim", args_schema=EsimSearchInput)
def search_esim(
    query: str,
) -> str:
    """
    Search for eSIM information.

    Args:
        query: Search query for eSIM information, for examples: eSIM for Japan, data plans for Europe, etc.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    data = {
        "collection_names": [esim_knowledge_base],
        "query": query,
        "k": 5,
        "hybrid": False
    }
    logger.info(f"Searching eSIM information with query: {query}")

    try:
        result = make_request("POST", "/query/collection", data)
        if result:
            return json.dumps(result)
        return "No eSIM information available."
    except ToolException as e:
        return str(e)


@tool("get_more_esim_info")
def get_more_esim_info(
    esim_plan: str,
) -> str:
    """
    Retrieve detailed information for a specific eSIM plan.
    Use this for fetching eSIM data by plan name or details.

    Args:
        esim_plan: The eSIM plan name or details to retrieve information for.

    Returns:
        A string containing the eSIM information in JSON format or an error message.
    """
    return f"You could fetch your results from search_esim tool to get more information about the eSIM plan {esim_plan}. Return the result to supervisor after that."


@tool("booking_esim")
def booking_esim() -> str:
    """
    Provides a template for booking an eSIM after a user has selected one.

    Returns:
        A string containing instructions for confirming eSIM bookings.
    """
    return """
    Confirm the selected eSIM plan with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """