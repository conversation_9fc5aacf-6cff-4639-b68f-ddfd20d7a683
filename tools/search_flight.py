from pydantic import BaseModel, <PERSON>
from typing import Optional
import requests
import json
import logging
from datetime import datetime,timedelta
from langchain_core.tools import tool, ToolException
from utils.config import FLIGHT_API_URL, FLIGHT_API_TOKEN


# Define input schemas for better type checking and documentation
class FlightSearchInput(BaseModel):
    """Input schema for flight search"""
    from_city_code: str = Field(description="IATA code of the departure city (e.g., 'HAN')")
    to_city_code: str = Field(description="IATA code of the destination city (e.g., 'SGN')")
    depart_date: str = Field(description="Departure date in ISO format (e.g., '2025-05-12T10:00:21.505764')")
    cabin_class: Optional[str] = Field(default="", description="Cabin class, must be 'Economy' or 'Business' (optional)")
    flight_brands: Optional[str] = Field(default="", description="Preferred airline brand (optional)")
    return_date: Optional[str] = Field(default="", description="Return date for round trip in ISO format (optional)")

@tool("search_flight", args_schema=FlightSearchInput)
def search_flight(
    from_city_code: str,
    to_city_code: str,
    depart_date: str,
    cabin_class: str = "",
    flight_brands: str = "",
    return_date: str = "",
) -> str:
    """
    Search for available flights based on origin, destination, travel dates,
    and optional preferences.

    Use one of these Origin and Destination Code for from_city_code/to_city_code argument:
        Hà Nội - HAN
        Hải Phòng - HPH
        Điện Biên - DIN
        Vân Đồn - VDO
        TP. Hồ Chí Minh - SGN
        Phú Quốc - PQC
        Côn Đảo - VCS
        Cần Thơ - VCA
        Cà Mau - CAH
        Rạch Giá - VKG
        Đà Nẵng - DAD
        Đà Lạt - DLI
        Nha Trang - CXR
        Vinh - VII
        Huế - HUI
        Thanh Hóa - THD
        Buôn Ma Thuột - BMV
        Pleiku - PXU
        Quy Nhơn - UIH
        Đồng Hới - VDH
        Tuy Hòa - TBB
        Chu Lai - VCL
        Bangkok - BKK
        Singapore - SIN
        Kuala Lumpur - KUL
        Tokyo - TYO
        Seoul Incheon Intl - ICN
        Taipei Taoyuan - TPE
        Paris - PAR
        London - LON
        Frankfurt - FRA
        Amsterdam - AMS
        Berlin - BER
        Zurich - ZRH
        Stockholm - STO
        Copenhagen - CPH
        Los Angeles - LAX
        San Francisco - SFO
        Melbourne - MEL
        Sydney - SYD
        Toronto - YTO
        New York - NYC
        Houston - HOU
        Dallas - DFW

    Args:
        from_city_code: IATA code of the departure city (e.g., "HAN").
        to_city_code: IATA code of the destination city (e.g., "SGN").
        depart_date: Departure date in ISO format (e.g., '2025-05-12T10:00:21.505764').
        cabin_class: Cabin class, must be "Economy" or "Business" (optional).
        flight_brands: Preferred airline brand (optional).
        return_date: Return date for round trip in ISO format (optional).

    Returns:
        A string containing the search results in JSON format or an error message.
    """
    # Get API token from config if available
    token = FLIGHT_API_TOKEN
    base_url = FLIGHT_API_URL

    headers = {
        "accept": "application/json",
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }

    # Calculate default return date if not provided
    return_date_default = datetime.fromisoformat(depart_date) + timedelta(days=7)
    return_date = (
        return_date if return_date != "" else return_date_default.isoformat()
    )

    params = {
        "FromCityCode": from_city_code,
        "ToCityCode": to_city_code,
        "FromDate": depart_date,
        "ToDate": return_date,
        "PageSize": 2000,
    }
    if cabin_class != "":
        params["CabinClass"] = cabin_class
    if flight_brands != "":
        params["FlightBrand"] = flight_brands

    logging.info(
        f"Flight search from {from_city_code} to {to_city_code} in {depart_date}, class {cabin_class}, brand {flight_brands}, return {return_date}"
    )

    try:
        logging.info(f"Making API request to: {base_url} with timeout: 5s")

        response = requests.get(
            base_url, params=params, headers=headers, timeout=5
        )

        # Log the response status and headers
        logging.info(f"Response status: {response.status_code}")
        logging.info(f"Response headers: {response.headers}")

        response.raise_for_status()  # raise error for bad status

        # Try to parse the JSON response
        try:
            data = response.json()
            logging.info(f"Response data structure: {list(data.keys())}")

            if "result" in data and "items" in data["result"] and data["result"]["items"]:
                logging.info(f"Found {len(data['result']['items'])} flights")
                return json.dumps(data["result"]["items"])
            else:
                logging.warning(f"No flights found in response: {data}")
                return "No Flight available."

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response content: {response.text[:500]}...")  # Log first 500 chars
            raise ToolException(f"Error: Invalid response format from flight API: {e}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        raise ToolException(f"Error searching for flights: {str(e)}")

@tool("get_more_flight_info")
def get_more_flight_info(
    flight_details: str,
) -> str:
    """
    Retrieve detailed information for a specific flight.
    Use this for fetching flight data by flight details.

    Args:
        flight_details: The flight details (airline, flight number, route, etc.) to retrieve information for.

    Returns:
        A string containing the flight information in JSON format or an error message.
    """
    return f"You could fetch your results from search_flight tool to get more information about the flight {flight_details}. Return the result to supervisor after that."


@tool("booking_flight")
def booking_flight() -> str:
    """
    Provides a template for booking a flight after a user has selected one.

    Returns:
        A string containing instructions for confirming flight bookings.
    """

    return f"""
    Confirm the selected flight with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """
