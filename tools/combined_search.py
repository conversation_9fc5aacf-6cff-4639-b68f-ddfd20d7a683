from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import json
import logging
from langchain_core.tools import tool, ToolException
from tools.search_company_info import search_company_info
from tools.search_web import search_web


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CombinedSearchInput(BaseModel):
    """Input schema for combined search"""
    query: str = Field(description="Search query for information")
    category: Optional[str] = Field(
        default="all",
        description="Category to search in company info: 'rovi_travel', 'vhi', 'teambuilding', 'gala_dinner', or 'all'"
    )
    search_depth: Optional[str] = Field(
        default="basic",
        description="Web search depth, either 'basic' (faster) or 'advanced' (more thorough)"
    )
    max_web_results: Optional[int] = Field(
        default=3,
        description="Maximum number of web search results to return (1-10)"
    )
    time_range: Optional[str] = Field(
        default=None,
        description="Time range for web results: 'day', 'week', 'month', or 'year'"
    )
    include_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains to include in the web search results"
    )
    exclude_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains to exclude from the web search results"
    )


@tool("combined_search", args_schema=CombinedSearchInput)
def combined_search(
    query: str,
    category: str = "all",
    search_depth: str = "basic",
    max_web_results: int = 3,
    time_range: Optional[str] = None,
    include_domains: Optional[List[str]] = None,
    exclude_domains: Optional[List[str]] = None
) -> str:
    """
    Search for information in both company knowledge bases and the web.

    Args:
        query: Search query for information.
        category: Category to search in company info: 'rovi_travel', 'vhi', 'teambuilding', 'gala_dinner', or 'all'.
        search_depth: Web search depth, either 'basic' (faster) or 'advanced' (more thorough).
        max_web_results: Maximum number of web search results to return (1-10).
        time_range: Time range for web results: 'day', 'week', 'month', or 'year'.
        include_domains: List of domains to include in the web search results.
        exclude_domains: List of domains to exclude from the web search results.

    Returns:
        A string containing the combined query results in JSON format or an error message.
    """
    logger.info(f"Performing combined search with query: {query}")

    # Get company information results
    try:
        company_info_results = search_company_info(query=query, category=category)
        company_info_data = json.loads(company_info_results) if not company_info_results.startswith("Error") and not company_info_results.startswith("No information") else {"error": company_info_results}
    except Exception as e:
        logger.error(f"Error in company info search: {str(e)}")
        company_info_data = {"error": f"Failed to retrieve company information: {str(e)}"}

    # Get web search results
    try:
        web_results = search_web(
            query=query,
            search_depth=search_depth,
            max_results=max_web_results,
            time_range=time_range,
            include_domains=include_domains,
            exclude_domains=exclude_domains
        )
        web_data = json.loads(web_results) if not web_results.startswith("Error") else {"error": web_results}
    except Exception as e:
        logger.error(f"Error in web search: {str(e)}")
        web_data = {"error": f"Failed to retrieve web information: {str(e)}"}

    # Combine the results
    combined_data = {
        "query": query,
        "company_information": company_info_data,
        "web_information": web_data
    }

    return json.dumps(combined_data, indent=2)