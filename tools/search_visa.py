from pydantic import BaseModel, Field
import json
import logging
from langchain_core.tools import tool, ToolException
from tools.search_kb import make_request


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Visa knowledge base ID
visa_knowledge_base = "8baa17d1-197d-45be-976f-3f1f18ecd6a9"


class VisaSearchInput(BaseModel):
    """Input schema for searching visa information"""
    query: str = Field(description="Search query for visa information")


@tool("search_visa", args_schema=VisaSearchInput)
def search_visa(
    query: str,
) -> str:
    """
    Search for visa information theo tên quốc gia được cung cấp.

    Args:
        query: Search query for visa information, for examples: Vietnam visa, Japan visa requirements, etc.

    Returns:
        A string containing the query results in JSON format or an error message.
    """
    data = {
        "collection_name": visa_knowledge_base,
        "query": query,
        "k": 1,
        "hybrid": False
    }
    logger.info(f"Searching visa information with query: {query}")

    try:
        result = make_request("POST", "/query/doc", data)
        if result:
            return json.dumps(result)
        return "No visa information available."
    except ToolException as e:
        return str(e)


@tool("get_more_visa_info")
def get_more_visa_info(
    visa_type: str,
) -> str:
    """
    Retrieve detailed information for a specific visa type.
    Use this for fetching visa data by visa type or country.

    Args:
        visa_type: The visa type or country to retrieve information for.

    Returns:
        A string containing the visa information in JSON format or an error message.
    """
    return f"You could fetch your results from search_visa tool to get more information about the visa {visa_type}. Return the result to supervisor after that."


@tool("booking_visa")
def booking_visa() -> str:
    """
    Provides a template for booking a visa after a user has selected one.

    Returns:
        A string containing instructions for confirming visa bookings.
    """
    return """
    Confirm the selected visa type with the user. If the user's name, phone number, or email address has not been provided, request this information. Once all details are collected, notify the that request has been confirmed the staff will contact to the user soon.
    """