from pydantic import BaseModel, <PERSON>
from typing import Optional
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from langchain_core.tools import tool, ToolException
from utils.config import EMAIL_SENDER, EMAIL_PASSWORD

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define input schema for better type checking and documentation
class SendEmailInput(BaseModel):
    """Input schema for sending an email"""
    to: str = Field(description="Comma-separated list of recipient email addresses")
    subject: str = Field(description="Subject of the email")
    content: str = Field(description="Content of the email")
    cc: Optional[str] = Field(default="", description="Comma-separated list of CC email addresses")

@tool("send_email", args_schema=SendEmailInput)
def send_email(
    to: str,
    subject: str,
    content: str,
    cc: str = "",
) -> str:
    """
    Send an email with a limit on the number of TO and CC recipients.

    Args:
        to: Comma-separated list of recipient email addresses.
        subject: Subject of the email.
        content: Content of the email.
        cc: Comma-separated list of CC email addresses (optional).

    Returns:
        A string message indicating the result of the email sending operation.
    """
    logger.info(f"Sending email to: {to}, subject: {subject}, cc: {cc}")

    # Validate environment variables
    if not EMAIL_SENDER or not EMAIL_PASSWORD:
        logger.error("Missing EMAIL_SENDER or EMAIL_PASSWORD environment variables")
        return "Error: EMAIL_SENDER or EMAIL_PASSWORD environment variables not configured."

    # Maximum number of recipients allowed
    MAX_RECIPIENTS = 5

    # Process TO list
    to_list = [email.strip() for email in to.split(",") if email.strip()]
    if len(to_list) > MAX_RECIPIENTS:
        logger.error(f"Too many TO recipients: {len(to_list)} exceeds limit of {MAX_RECIPIENTS}")
        return f"Error: Too many TO recipients ({len(to_list)} exceeds limit of {MAX_RECIPIENTS})."

    # Process CC list
    cc_list = [cc_email.strip() for cc_email in cc.split(",") if cc_email.strip()]
    if len(cc_list) > MAX_RECIPIENTS:
        logger.error(f"Too many CC recipients: {len(cc_list)} exceeds limit of {MAX_RECIPIENTS}")
        return f"Error: Too many CC recipients ({len(cc_list)} exceeds limit of {MAX_RECIPIENTS})."

    try:
        # Create email message
        msg = MIMEMultipart()
        msg["From"] = EMAIL_SENDER
        msg["To"] = ", ".join(to_list)
        msg["Subject"] = subject

        if cc_list:
            msg["Cc"] = ", ".join(cc_list)

        # Add email content (plain text with UTF-8 encoding)
        msg.attach(MIMEText(content, "plain", "utf-8"))

        # Combine TO and CC recipients
        recipient_list = to_list + cc_list

        # Connect to SMTP server and send email with 5-second timeout
        with smtplib.SMTP_SSL("smtp.larksuite.com", 465, timeout=5) as server:
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            server.sendmail(EMAIL_SENDER, recipient_list, msg.as_string())

        logger.info(f"Email sent successfully to {', '.join(to_list)} (CC: {', '.join(cc_list)})")
        return f"Email sent successfully to {', '.join(to_list)} (CC: {', '.join(cc_list)})!"

    except smtplib.SMTPException as e:
        logger.error(f"SMTP error while sending email: {str(e)}")
        raise ToolException(f"Error sending email: SMTP error - {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error while sending email: {str(e)}")
        raise ToolException(f"Error sending email: {str(e)}")

if __name__ == "__main__":
    print(send_email.invoke(
        {
            "to": "<EMAIL>",
            "subject": "Test email",
            "content": "This is a test email",
            "cc": "<EMAIL>"
        }
    ))
