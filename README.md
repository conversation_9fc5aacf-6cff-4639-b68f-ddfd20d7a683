# Travel Assistant with LangGraph

This project implements a travel assistant agent using LangGraph that can help users with various travel-related tasks such as flight search, tour booking, hotel reservations, and more.

## Travel Agent Implementation

The project includes a standalone travel agent implementation (`travel_agent.py`) that can be used directly in your code or through the LangGraph Server.

## Prerequisites

- Python 3.11 or higher
- LangSmith API key (provided: ``)
- OpenAI API key (already configured in .env)
- MongoDB (optional, for persistence)

## Setup

1. Install the LangGraph CLI:

```bash
pip install --upgrade "langgraph-cli[inmem]"
```

2. Install the project dependencies:

```bash
pip install -e .
```

3. Verify your .env file contains the necessary configuration:

```
# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini

## Running the LangGraph Server

1. Start the LangGraph server:

```bash
./run_langgraph_server.sh
```

Or manually:

```bash
# If MongoDB is enabled
langgraph dev --allow-blocking

# If using in-memory storage
langgraph dev
```

This will start the server on http://localhost:2024 by default.

2. You should see output similar to:

```
>    Ready!
>
>    - API: http://localhost:2024
>
>    - Docs: http://localhost:2024/docs
>
>    - LangGraph Studio Web UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
```

## Testing the Application

### Using the Python SDK

You can test the application using the synchronous client:

```bash
python test_langgraph_sync.py
```

### Using the REST API

You can also test the application using curl:

```bash
curl -s --request POST \
    --url "http://localhost:2024/runs/stream" \
    --header 'Content-Type: application/json' \
    --data "{
        \"assistant_id\": \"travel_agent\",
        \"input\": {
            \"messages\": [
                {
                    \"role\": \"human\",
                    \"content\": \"I need help planning a trip to Japan. Can you recommend some tour packages?\"
                }
            ]
        },
        \"stream_mode\": \"messages-tuple\"
    }"
```

## Monitoring with LangSmith

1. Visit [LangSmith](https://smith.langchain.com/) and log in with your account.

2. Navigate to the "Projects" section and look for the "travel-assistant" project.

3. You'll be able to see all the traces of your application, including:
   - Message exchanges
   - Node execution flow
   - State changes
   - Tool calls

4. You can also use LangGraph Studio to visualize and interact with your graph:
   - Visit the URL provided when starting the server: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
   - This provides a specialized UI for visualizing and debugging your graph

## Troubleshooting

- If you encounter connection issues with MongoDB, set `USE_MONGODB=false` in your .env file to use in-memory storage instead.
- If you encounter the "BlockingError: Blocking call to socket.socket.connect" error:
  1. Run with `langgraph dev --allow-blocking` (recommended for development)
  2. Or set `export BG_JOB_ISOLATED_LOOPS=true` before running `langgraph dev`
  3. Or disable MongoDB with `export USE_MONGODB=false`
- If you're using Safari, you may need to use the `--tunnel` flag when starting the server: `langgraph dev --tunnel`
- Make sure your LangSmith API key is correctly set in the .env file.

## Using the Standalone Travel Agent

You can use the travel agent directly in your code without the LangGraph Server:

```python
from travel_agent import create_travel_agent
from langchain_core.messages import HumanMessage

# Create the agent
travel_agent = create_travel_agent()

# Run the agent with a user query
result = travel_agent.invoke(
    {
        "messages": [
            HumanMessage(content="I need to find a flight from HAN to SGN on 2025-05-14")
        ],
        "user_info": None
    }
)

# Process the result
for message in result["messages"]:
    print(message)
```

Or run the example script:

```bash
python run_travel_agent.py
```

## Available Tools

The travel agent has access to the following tools:

- `search_flight`: Search for flights based on origin, destination, and dates
- `booking_flight`: Handle flight booking process
- `search_tour`: Search for tours based on user query
- `get_more_tour_info`: Get detailed information about a specific tour
- `booking_tour`: Handle tour booking process
- `search_hotel`: Search for hotels based on location and preferences
- `booking_hotel`: Handle hotel booking process
- `query_collections`: Query multiple knowledge base collections
- `query_doc`: Query a specific document collection
- `search_ticket`: Search for attraction tickets
- `create_lead`: Create a lead in the CRM system

## Additional Resources

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [LangSmith Documentation](https://docs.smith.langchain.com/)
- [LangGraph Server API Reference](https://langchain-ai.github.io/langgraph/cloud/reference/api/api_ref.html)
